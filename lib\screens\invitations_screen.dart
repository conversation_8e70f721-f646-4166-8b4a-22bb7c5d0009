import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../utils/navigation_service.dart';
import '../constants/design_constants.dart';

class InvitationsScreen extends StatefulWidget {
  @override
  _InvitationsScreenState createState() => _InvitationsScreenState();
}

class _InvitationsScreenState extends State<InvitationsScreen> {

  final List<Map<String, dynamic>> _allInvitations = [
    {
      'id': '1',
      'jobTitle': 'Senior Flutter Developer',
      'company': 'TechCorp GmbH',
      'receivedDate': '18. decembar 2024',
      'status': 'pending',
      'logo': '🏢',
      'description': 'Kompanija vas poziva na razgovor za poziciju Senior Flutter Developer.',
    },
  ];

  Map<String, dynamic> _getStatusConfig(String status) {
    switch (status) {
      case 'pending':
        return {
          'color': DesignConstants.modernWarningYellow,
          'bgColor': DesignConstants.modernWarningYellow.withOpacity(0.1),
          'icon': Icons.schedule_rounded,
          'label': 'Na čekanju'
        };
      case 'accepted':
        return {
          'color': DesignConstants.modernSuccessGreen,
          'bgColor': DesignConstants.modernSuccessGreen.withOpacity(0.1),
          'icon': Icons.check_circle_rounded,
          'label': 'Prihvaćeno'
        };
      default:
        return {
          'color': DesignConstants.modernWarningYellow,
          'bgColor': DesignConstants.modernWarningYellow.withOpacity(0.1),
          'icon': Icons.schedule_rounded,
          'label': 'Na čekanju'
        };
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            DesignConstants.backgroundLight,
            DesignConstants.white,
          ],
        ),
      ),
      child: _allInvitations.isNotEmpty
          ? _buildInvitationsList(_allInvitations)
          : _buildEmptyState(),
    );
  }

  Widget _buildInvitationsList(List<Map<String, dynamic>> invitations) {
    return ListView.builder(
      padding: EdgeInsets.fromLTRB(DesignConstants.spaceL, DesignConstants.spaceM, DesignConstants.spaceL, 80),
      itemCount: invitations.length,
      itemBuilder: (context, index) {
        final invitation = invitations[index];
        final statusConfig = _getStatusConfig(invitation['status'])!;
        final isPending = invitation['status'] == 'pending';
        
        return Container(
          margin: EdgeInsets.only(bottom: DesignConstants.spaceL),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                DesignConstants.white,
                DesignConstants.modernPrimaryBlue.withOpacity(0.01),
              ],
            ),
            borderRadius: BorderRadius.circular(DesignConstants.radiusXLarge),
            border: Border.all(
              color: DesignConstants.modernPrimaryBlue.withOpacity(0.1),
              width: 1.5,
            ),
            boxShadow: [
              BoxShadow(
                color: DesignConstants.modernPrimaryBlue.withOpacity(0.05),
                blurRadius: 12,
                offset: Offset(0, 4),
                spreadRadius: 1,
              ),
              BoxShadow(
                color: Colors.black.withOpacity(0.03),
                blurRadius: 8,
                offset: Offset(0, 2),
              ),
            ],
          ),
          child: Material(
            color: Colors.transparent,
            borderRadius: BorderRadius.circular(DesignConstants.radiusXLarge),
            child: Padding(
              padding: EdgeInsets.all(DesignConstants.spaceXL),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Container(
                        width: 56,
                        height: 56,
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                            colors: [
                              DesignConstants.modernPrimaryBlue.withOpacity(0.1),
                              DesignConstants.modernPrimaryBlue.withOpacity(0.05),
                            ],
                          ),
                          borderRadius: BorderRadius.circular(DesignConstants.radiusLarge),
                          border: Border.all(
                            color: DesignConstants.modernPrimaryBlue.withOpacity(0.15),
                            width: 1,
                          ),
                        ),
                        child: Center(
                          child: Text(
                            invitation['logo'],
                            style: TextStyle(fontSize: 24),
                          ),
                        ),
                      ),
                      SizedBox(width: DesignConstants.spaceM),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              invitation['jobTitle'],
                              style: DesignConstants.titleSmall.copyWith(
                                color: DesignConstants.black,
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                            SizedBox(height: DesignConstants.spaceXS),
                            Text(
                              invitation['company'],
                              style: DesignConstants.bodyMedium.copyWith(
                                color: DesignConstants.modernPrimaryBlue,
                              ),
                            ),
                          ],
                        ),
                      ),
                      if (!isPending)
                        Container(
                          padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                          decoration: BoxDecoration(
                            color: statusConfig['bgColor'],
                            borderRadius: BorderRadius.circular(DesignConstants.radiusMedium),
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(
                                statusConfig['icon'],
                                size: 14,
                                color: statusConfig['color'],
                              ),
                              SizedBox(width: DesignConstants.spaceXS),
                              Text(
                                statusConfig['label'],
                                style: DesignConstants.labelSmall.copyWith(
                                  color: statusConfig['color'],
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ],
                          ),
                        ),
                    ],
                  ),
                  
                  SizedBox(height: DesignConstants.spaceM),
                  
                  // Description
                  Text(
                    invitation['description'],
                    style: DesignConstants.bodySmall.copyWith(
                      color: DesignConstants.darkGray,
                      height: 1.4,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  
                  SizedBox(height: 16),
                  
                  // Footer with date and buttons
                  Row(
                    children: [
                      Icon(
                        Icons.calendar_today_outlined,
                        size: 14,
                        color: const Color(0xFF64748B),
                      ),
                      SizedBox(width: DesignConstants.spaceXS),
                      Text(
                        invitation['receivedDate'],
                        style: DesignConstants.captionMedium.copyWith(
                          color: DesignConstants.darkGray,
                        ),
                      ),
                      Spacer(),
                      if (isPending) 
                        Container(
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(DesignConstants.radiusLarge),
                            boxShadow: DesignConstants.modernBrandButtonShadow,
                          ),
                          child: ElevatedButton(
                            onPressed: () => _viewInvitation(invitation['id']),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: DesignConstants.modernPrimaryBlue,
                              foregroundColor: DesignConstants.white,
                              padding: EdgeInsets.symmetric(
                                horizontal: DesignConstants.spaceL,
                                vertical: DesignConstants.spaceS,
                              ),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(DesignConstants.radiusLarge),
                              ),
                              elevation: 0,
                              shadowColor: Colors.transparent,
                            ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(
                                Icons.visibility_outlined,
                                size: 16,
                                color: Colors.white,
                              ),
                              SizedBox(width: DesignConstants.spaceXS),
                              Text(
                                'Pogledaj',
                                style: DesignConstants.buttonSmall,
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Padding(
        padding: EdgeInsets.all(DesignConstants.spaceMassive),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    DesignConstants.modernPrimaryBlue.withOpacity(0.1),
                    DesignConstants.modernPrimaryBlue.withOpacity(0.05),
                  ],
                ),
                shape: BoxShape.circle,
                border: Border.all(
                  color: DesignConstants.modernPrimaryBlue.withOpacity(0.2),
                  width: 2,
                ),
                boxShadow: DesignConstants.shadowSmall,
              ),
              child: Icon(
                Icons.mail_outline_rounded,
                size: DesignConstants.iconXLarge,
                color: DesignConstants.modernPrimaryBlue,
              ),
            ),
            SizedBox(height: DesignConstants.spaceXL),
            Text(
              'Nema pozivnica',
              style: DesignConstants.titleMedium.copyWith(
                color: DesignConstants.black,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: DesignConstants.spaceS),
            Text(
              'Pozivnice od poslodavaca će se pojaviti ovdje',
              style: DesignConstants.bodyMedium.copyWith(
                color: DesignConstants.darkGray,
                height: 1.4,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  void _viewInvitation(String invitationId) {
    NavigationService().navigateToScreen('invitation_job_details', data: {
      'invitationId': invitationId,
    });
  }

}