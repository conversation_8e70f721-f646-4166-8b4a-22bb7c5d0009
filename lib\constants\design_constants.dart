import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class DesignConstants {
  // ========== LEGACY COLORS (kept for compatibility) ==========
  static const Color primaryBlue = Color(0xFF1E3A8A);
  static const Color secondaryBlue = Color(0xFF3B82F6);
  static const Color accentOrange = Color(0xFFF59E0B);
  static const Color successGreen = Color(0xFF10B981);
  static const Color errorRed = Color(0xFFEF4444);
  static const Color textPrimary = Color(0xFF0F172A);
  static const Color textSecondary = Color(0xFF64748B);
  static const Color backgroundLight = Color(0xFFFAFBFC);
  static const Color backgroundGray = Color(0xFFF1F5F9);
  static const Color borderGray = Color(0xFFE2E8F0);
  
  // ========== NEW MODERN BRAND COLORS ==========
  
  // Primary Brand Colors (New Modern Palette)
  static const Color modernPrimaryBlue = Color(0xFF2665D0);
  static const Color modernPrimaryBlueDark = Color(0xFF1E4DB7);
  static const Color modernPrimaryBlueDeep = Color(0xFF1A3F8C);
  
  // Additional Modern Colors
  static const Color white = Colors.white;
  static const Color black = Color(0xFF0F172A);
  static const Color darkGray = Color(0xFF64748B);
  static const Color lightGray = Color(0xFFE2E8F0);
  static const Color surfaceGray = Color(0xFFF8FAFC);
  
  // Enhanced Status Colors
  static const Color modernSuccessGreen = Color(0xFF16A34A);
  static const Color modernSuccessGreenLight = Color(0xFFDCFCE7);
  static const Color modernSuccessGreenDark = Color(0xFF15803D);
  static const Color modernWarningYellow = Color(0xFFFBBF24);
  static const Color modernErrorRed = Color(0xFFEF4444);

  // ========== BORDER RADIUS ==========
  static const double radiusSmall = 8.0;
  static const double radiusMedium = 12.0;
  static const double radiusLarge = 16.0;
  
  // Extended Modern Border Radius
  static const double radiusXLarge = 20.0;
  static const double radiusXXLarge = 24.0;
  static const double radiusHuge = 28.0;

  // ========== SPACING ==========
  static const double spaceXS = 4.0;
  static const double spaceS = 8.0;
  static const double spaceM = 12.0;
  static const double spaceL = 16.0;
  static const double spaceXL = 20.0;
  static const double spaceXXL = 24.0;
  
  // Extended Modern Spacing
  static const double spaceHuge = 32.0;
  static const double spaceMassive = 40.0;

  // ========== ICON SIZES ==========
  static const double iconSmall = 16.0;
  static const double iconMedium = 20.0;
  static const double iconLarge = 24.0;
  
  // Extended Icon Sizes
  static const double iconXLarge = 32.0;
  static const double iconXXLarge = 40.0;

  // Shadows
  static List<BoxShadow> shadowSmall = [
    BoxShadow(
      color: Colors.black.withOpacity(0.05),
      blurRadius: 4,
      offset: Offset(0, 2),
    ),
  ];

  static List<BoxShadow> shadowMedium = [
    BoxShadow(
      color: Colors.black.withOpacity(0.08),
      blurRadius: 8,
      offset: Offset(0, 4),
    ),
  ];

  static List<BoxShadow> shadowLarge = [
    BoxShadow(
      color: Colors.black.withOpacity(0.1),
      blurRadius: 16,
      offset: Offset(0, 8),
    ),
  ];

  static List<BoxShadow> shadowColored(Color color) => [
    BoxShadow(
      color: color.withOpacity(0.2),
      blurRadius: 8,
      offset: Offset(0, 4),
    ),
  ];

  // ========== MODERN GRADIENTS ==========
  
  // Main Brand Gradient
  static const LinearGradient modernPrimaryGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [modernPrimaryBlue, modernPrimaryBlueDark, modernPrimaryBlueDeep],
    stops: [0.0, 0.6, 1.0],
  );
  
  // App Bar Gradient (lighter)
  static const LinearGradient modernAppBarGradient = LinearGradient(
    begin: Alignment.topCenter,
    end: Alignment.center,
    colors: [modernPrimaryBlue, modernPrimaryBlue],
    stops: [0.0, 0.3],
  );
  
  // Bottom Navigation Gradient
  static const LinearGradient modernBottomNavGradient = LinearGradient(
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
    colors: [modernPrimaryBlue, modernPrimaryBlueDark],
  );
  
  // ========== MODERN SHADOWS ==========
  
  static List<BoxShadow> get modernGlassShadow => [
    BoxShadow(
      color: Colors.black.withOpacity(0.1),
      blurRadius: 20,
      offset: const Offset(0, 8),
    ),
  ];
  
  static List<BoxShadow> get modernButtonShadow => [
    BoxShadow(
      color: Colors.black.withOpacity(0.15),
      blurRadius: 16,
      offset: const Offset(0, 6),
    ),
  ];
  
  static List<BoxShadow> get modernModalShadow => [
    BoxShadow(
      color: Colors.black.withOpacity(0.3),
      blurRadius: 30,
      offset: const Offset(0, 15),
    ),
  ];
  
  static List<BoxShadow> get modernBottomNavShadow => [
    BoxShadow(
      color: Colors.black.withOpacity(0.1),
      blurRadius: 20,
      offset: const Offset(0, -5),
    ),
  ];
  
  static List<BoxShadow> get modernBrandButtonShadow => [
    BoxShadow(
      color: modernPrimaryBlue.withOpacity(0.3),
      blurRadius: 16,
      offset: const Offset(0, 8),
    ),
  ];

  // ========== MODERN OPACITY VALUES ==========
  
  // Glass Morphism Effects
  static const double glassOpacityLight = 0.1;
  static const double glassOpacityMedium = 0.15;
  static const double glassOpacityStrong = 0.2;
  
  // Border Opacities
  static const double borderOpacityLight = 0.2;
  static const double borderOpacityMedium = 0.3;
  static const double borderOpacityStrong = 0.5;
  
  // Text Opacities
  static const double textOpacityHigh = 0.95;
  static const double textOpacityMedium = 0.9;
  static const double textOpacityLow = 0.8;
  static const double textOpacitySubtle = 0.6;

  // ========== MODERN GLASS MORPHISM DECORATIONS ==========
  
  static BoxDecoration get modernGlassContainer => BoxDecoration(
    color: white.withOpacity(glassOpacityMedium),
    borderRadius: BorderRadius.circular(radiusXXLarge),
    border: Border.all(
      color: white.withOpacity(borderOpacityMedium),
      width: 1,
    ),
    boxShadow: modernGlassShadow,
  );
  
  static BoxDecoration get modernGlassCard => BoxDecoration(
    color: white.withOpacity(glassOpacityLight),
    borderRadius: BorderRadius.circular(radiusXLarge),
    border: Border.all(
      color: white.withOpacity(borderOpacityLight),
      width: 1,
    ),
    boxShadow: modernGlassShadow,
  );
  
  static BoxDecoration get modernGlassButton => BoxDecoration(
    color: white.withOpacity(glassOpacityMedium),
    borderRadius: BorderRadius.circular(radiusLarge),
    border: Border.all(
      color: white.withOpacity(borderOpacityMedium),
      width: 1,
    ),
  );

  // ========== COMPREHENSIVE TEXT STYLES ==========
  
  // ========== HEADLINES (Outfit Font) ==========
  static TextStyle get headlineXLarge => GoogleFonts.outfit(
    fontSize: 36,
    fontWeight: FontWeight.w800,
    color: black,
    letterSpacing: -1.5,
    height: 1.1,
  );
  
  static TextStyle get headlineLarge => GoogleFonts.outfit(
    fontSize: 32,
    fontWeight: FontWeight.w800,
    color: black,
    letterSpacing: -1.2,
    height: 1.1,
  );
  
  static TextStyle get headlineMedium => GoogleFonts.outfit(
    fontSize: 28,
    fontWeight: FontWeight.w700,
    color: black,
    letterSpacing: -0.8,
    height: 1.2,
  );
  
  static TextStyle get headlineSmall => GoogleFonts.outfit(
    fontSize: 24,
    fontWeight: FontWeight.w700,
    color: black,
    letterSpacing: -0.5,
    height: 1.2,
  );
  
  static TextStyle get headlineXSmall => GoogleFonts.outfit(
    fontSize: 20,
    fontWeight: FontWeight.w600,
    color: black,
    letterSpacing: -0.3,
    height: 1.3,
  );
  
  // ========== TITLES (Inter Font - Medium Weight) ==========
  static TextStyle get titleLarge => GoogleFonts.inter(
    fontSize: 22,
    fontWeight: FontWeight.w600,
    color: black,
    height: 1.3,
  );
  
  static TextStyle get titleMedium => GoogleFonts.inter(
    fontSize: 18,
    fontWeight: FontWeight.w600,
    color: white,
    height: 1.3,
  );
  
  static TextStyle get titleSmall => GoogleFonts.inter(
    fontSize: 16,
    fontWeight: FontWeight.w600,
    color: black,
    height: 1.4,
  );
  
  // ========== BODY TEXT (Inter Font - Regular Weight) ==========
  static TextStyle get bodyXLarge => GoogleFonts.inter(
    fontSize: 18,
    fontWeight: FontWeight.w400,
    color: black,
    height: 1.5,
  );
  
  static TextStyle get bodyLarge => GoogleFonts.inter(
    fontSize: 16,
    fontWeight: FontWeight.w400,
    color: black,
    height: 1.5,
  );
  
  static TextStyle get bodyMedium => GoogleFonts.inter(
    fontSize: 15,
    fontWeight: FontWeight.w400,
    color: darkGray,
    height: 1.5,
  );
  
  static TextStyle get bodySmall => GoogleFonts.inter(
    fontSize: 14,
    fontWeight: FontWeight.w400,
    color: darkGray,
    height: 1.4,
  );
  
  static TextStyle get bodyXSmall => GoogleFonts.inter(
    fontSize: 12,
    fontWeight: FontWeight.w400,
    color: darkGray,
    height: 1.4,
  );
  
  // ========== LABELS (Inter Font - Medium Weight) ==========
  static TextStyle get labelLarge => GoogleFonts.inter(
    fontSize: 16,
    fontWeight: FontWeight.w500,
    color: black,
  );
  
  static TextStyle get labelMedium => GoogleFonts.inter(
    fontSize: 14,
    fontWeight: FontWeight.w500,
    color: darkGray,
  );
  
  static TextStyle get labelSmall => GoogleFonts.inter(
    fontSize: 12,
    fontWeight: FontWeight.w500,
    color: darkGray,
  );
  
  // ========== CAPTIONS (Inter Font - Regular Weight) ==========
  static TextStyle get captionLarge => GoogleFonts.inter(
    fontSize: 14,
    fontWeight: FontWeight.w400,
    color: darkGray,
  );
  
  static TextStyle get captionMedium => GoogleFonts.inter(
    fontSize: 12,
    fontWeight: FontWeight.w400,
    color: darkGray,
  );
  
  static TextStyle get captionSmall => GoogleFonts.inter(
    fontSize: 10,
    fontWeight: FontWeight.w400,
    color: darkGray,
  );
  
  // ========== BUTTON TEXT STYLES ==========
  static TextStyle get buttonLarge => GoogleFonts.inter(
    fontSize: 16,
    fontWeight: FontWeight.w700,
    color: white,
  );
  
  static TextStyle get buttonMedium => GoogleFonts.inter(
    fontSize: 14,
    fontWeight: FontWeight.w600,
    color: white,
  );
  
  static TextStyle get buttonSmall => GoogleFonts.inter(
    fontSize: 12,
    fontWeight: FontWeight.w600,
    color: white,
  );
  
  // ========== MODERN VARIATIONS (for gradient backgrounds) ==========
  static TextStyle get modernHeadlineLarge => GoogleFonts.outfit(
    fontSize: 32,
    fontWeight: FontWeight.w800,
    color: white,
    letterSpacing: -1.2,
    height: 1.1,
  );
  
  static TextStyle get modernHeadlineMedium => GoogleFonts.outfit(
    fontSize: 28,
    fontWeight: FontWeight.w700,
    color: white,
    letterSpacing: -0.8,
    height: 1.2,
  );
  
  static TextStyle get modernBodyLarge => GoogleFonts.inter(
    fontSize: 16,
    fontWeight: FontWeight.w400,
    color: white.withOpacity(textOpacityMedium),
    height: 1.5,
  );
  
  static TextStyle get modernBodyMedium => GoogleFonts.inter(
    fontSize: 15,
    fontWeight: FontWeight.w400,
    color: white.withOpacity(textOpacityMedium),
    height: 1.5,
  );
  
  static TextStyle get modernButtonPrimary => GoogleFonts.outfit(
    fontSize: 18,
    fontWeight: FontWeight.w700,
    letterSpacing: 0.3,
  );
  
  static TextStyle get modernButtonSecondary => GoogleFonts.inter(
    fontSize: 16,
    fontWeight: FontWeight.w600,
    letterSpacing: 0.1,
  );

  // ========== MODERN FORM FIELD DECORATIONS ==========
  
  static InputDecoration getModernGlassInputDecoration({
    required String labelText,
    required String hintText,
  }) {
    return InputDecoration(
      labelText: labelText,
      hintText: hintText,
      labelStyle: GoogleFonts.inter(
        color: white.withOpacity(textOpacityLow),
        fontWeight: FontWeight.w500,
      ),
      hintStyle: GoogleFonts.inter(
        color: white.withOpacity(textOpacitySubtle),
      ),
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(radiusLarge),
        borderSide: BorderSide(color: white.withOpacity(borderOpacityMedium)),
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(radiusLarge),
        borderSide: BorderSide(color: white.withOpacity(borderOpacityMedium)),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(radiusLarge),
        borderSide: const BorderSide(color: white, width: 2),
      ),
      filled: true,
      fillColor: white.withOpacity(glassOpacityLight),
    );
  }

  // ========== MODERN BUTTON STYLES ==========
  
  static ButtonStyle get modernPrimaryButtonStyle => ElevatedButton.styleFrom(
    backgroundColor: white,
    foregroundColor: modernPrimaryBlue,
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.circular(radiusXLarge),
    ),
    elevation: 0,
    shadowColor: Colors.transparent,
    padding: const EdgeInsets.symmetric(vertical: 16),
  );
  
  static ButtonStyle get modernSecondaryButtonStyle => TextButton.styleFrom(
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.circular(radiusLarge),
    ),
    padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
  );
  
  static ButtonStyle get modernGlassButtonStyle => TextButton.styleFrom(
    backgroundColor: white.withOpacity(glassOpacityLight),
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.circular(radiusLarge),
      side: BorderSide(
        color: white.withOpacity(borderOpacityLight),
        width: 1,
      ),
    ),
    padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
  );

  // ========== ANIMATION DURATIONS ==========
  
  static const Duration animationFast = Duration(milliseconds: 200);
  static const Duration animationMedium = Duration(milliseconds: 300);
  static const Duration animationSlow = Duration(milliseconds: 500);

  // ========== RESPONSIVE HELPERS ==========
  
  static double getResponsiveFontSize(BuildContext context, double baseSize) {
    final screenWidth = MediaQuery.of(context).size.width;
    return screenWidth * (baseSize / 375); // 375 is base iPhone screen width
  }
  
  static double getResponsiveWidth(BuildContext context, double percentage) {
    return MediaQuery.of(context).size.width * percentage;
  }
  
  static double getResponsiveHeight(BuildContext context, double percentage) {
    return MediaQuery.of(context).size.height * percentage;
  }
}

// ========== EXTENSION FOR EASY COLOR USAGE ==========
extension ModernDesignColorsExtension on Color {
  Color get withGlassOpacity => withOpacity(DesignConstants.glassOpacityMedium);
  Color get withBorderOpacity => withOpacity(DesignConstants.borderOpacityMedium);
  Color get withTextOpacity => withOpacity(DesignConstants.textOpacityMedium);
}