# Vodič za implementaciju Obrazovanja i Radnog iskustva u Onboarding

## Pregled promjena

Trenutni onboarding ima **4 koraka**, trebamo dodati **2 nova koraka**:
- **Korak 5: Obrazovanje**
- **Korak 6: <PERSON><PERSON><PERSON> iskustvo**

**Ukupno koraka:** 6 (umjesto trenutnih 4)

---

## Korak 5: Obrazovanje

### Ključne funkcionalnosti:
1. **Nivo obrazovanja** - dropdown sa opcijama:
   - Os<PERSON>na škola
   - Srednja škola 
   - <PERSON><PERSON><PERSON>
   - Fakultet
   - Master
   - Doktorat

2. **Smjer/Zvanje** - text input za unos specijalizacije

3. **<PERSON><PERSON>kole** - text input

4. **M<PERSON><PERSON> škole** - text input za grad/mjesto

5. **Period obrazovanja** - intuitivni datum picker:
   - **Format:** Mjesec + God<PERSON> (MM/YY kao LinkedIn)
   - **Implementacija:** Dva dropdown-a jedan do drugog
   - **M<PERSON><PERSON><PERSON>:** Jan, Feb, Mar... (skrać<PERSON> na srpskom)
   - **<PERSON><PERSON>:** Poslednih 50 godina

### UX napomene za Obrazovanje:
- Jednostavan layout sa jasnim labelima
- Velika input polja za lakši unos na mobilnim uređajima
- Placeholder text sa primerima relevantnim za ciljnu grupu
- Validacija: sva polja osim smjera su obavezna

---

## Korak 6: Radno iskustvo

### Ključne funkcionalnosti:

#### 1. Opcija "Nemam radno iskustvo"
- **Checkbox na vrhu stranice**
- Kada je označen, sakriva sve ostale opcije
- Omogućuje mladim radnicima da prođu onboarding

#### 2. Dodavanje radnih iskustava
- **Dinamičko dodavanje** - korisnik može dodati više iskustava
- **Button:** "Dodaj radno iskustvo" / "Dodaj još jedno iskustvo"
- **Numeracija:** Svaki card ima broj (1, 2, 3...)

#### 3. Card za svako radno iskustvo:

**Header sa kontrolama:**
- Broj iskustva u plavom badge-u
- Naslov "Radno iskustvo X"
- Delete button (trash icon) u desnom uglu

**Input polja:**
- **Pozicija/Radno mjesto** - sa placeholder primerima za ciljnu grupu
- **Kompanija** - naziv firme
- **Mjesto** - grad/mjesto rada

**Period rada:**
- **Checkbox:** "Trenutno radim ovdje"
- **Datum od** - uvek obavezan (mjesec/godina picker)
- **Datum do** - samo ako ne radi trenutno
- **Vizuelna oznaka** za trenutni posao (zeleni badge "Trenutno zapošljen")

### UX napomene za Radno iskustvo:
- **Card design** - svako iskustvo je u odvojenoj kartici
- **Jasna hijerarhija** - numeracija i header-i
- **Intuitivno dodavanje/brisanje** - veliki button-i sa ikonama
- **Validacija po card-u** - svaki card mora biti potpuno popunjen
- **Uslovni unos** - "Do kada" se sakriva ako radi trenutno

---

## Implementacione izmjene

### 1. U `_OnboardingScreenState` klasi:

```dart
// Promeniti totalSteps
final int totalSteps = 6; // umesto 4

// Dodati nove state varijable za obrazovanje
String? educationLevel;
String? fieldOfStudy;
String? schoolName;
String? schoolLocation;
int? educationStartMonth;
int? educationStartYear;
int? educationEndMonth;
int? educationEndYear;

// Dodati varijable za radno iskustvo
List<WorkExperience> workExperiences = [];
bool hasNoWorkExperience = false;
```

### 2. Proširiti `_canProceed()` metodu:

```dart
bool _canProceed() {
  switch (currentStep) {
    // ... postojeći case-ovi ...
    case 5: // Obrazovanje
      return educationLevel != null && 
             educationStartMonth != null && 
             educationStartYear != null &&
             educationEndMonth != null &&
             educationEndYear != null;
    case 6: // Radno iskustvo
      if (hasNoWorkExperience) return true;
      return workExperiences.isNotEmpty && 
             workExperiences.every((exp) => 
               exp.position != null && exp.position!.isNotEmpty &&
               exp.company != null && exp.company!.isNotEmpty &&
               exp.startMonth != null && exp.startYear != null &&
               (exp.isCurrentJob || (exp.endMonth != null && exp.endYear != null))
             );
    default:
      return false;
  }
}
```

### 3. Dodati nove step metode u build():

```dart
if (currentStep == 5) _buildStep5Education(),
if (currentStep == 6) _buildStep6WorkExperience(),
```

### 4. Kreirati WorkExperience model:

```dart
class WorkExperience {
  String? position;
  String? company;
  String? location;
  int? startMonth;
  int? startYear;
  int? endMonth;
  int? endYear;
  bool isCurrentJob = false;

  WorkExperience({
    this.position,
    this.company,
    this.location,
    this.startMonth,
    this.startYear,
    this.endMonth,
    this.endYear,
    this.isCurrentJob = false,
  });
}
```

---

## Dizajn specifikacije

### Boje (iz postojeće teme):
- **Primary plava:** `#1E3A8A`
- **Background:** `#FAFBFC`
- **Card background:** `#FFFFFF`
- **Border:** `#E2E8F0`
- **Text primary:** `#0F172A`
- **Text secondary:** `#64748B`
- **Success zelena:** `#16A34A`
- **Error crvena:** `#EF4444`

### Tipografija:
- **Naslovi:** Inter, 24px, Font Weight 700
- **Podnaslovi:** Inter, 18px, Font Weight 600
- **Labels:** Inter, 16px, Font Weight 600
- **Body text:** Inter, 16px, Font Weight 400
- **Helper text:** Inter, 14px, Font Weight 400

### Spacing:
- **Card padding:** 24px
- **Element spacing:** 16px, 24px, 32px
- **Border radius:** 12px za card-ove, 8px za input-e

---

## Validacija i error handling

### Obrazovanje:
- Sva polja obavezna osim "Smjer/Zvanje"
- End date mora biti posle start date-a
- Godine ne mogu biti u budućnosti

### Radno iskustvo:
- Ako nije označeno "Nemam iskustvo", mora postojati bar jedno iskustvo
- Sva polja u card-u su obavezna
- Start date obavezan uvek
- End date obavezan samo ako nije trenutni posao
- End date mora biti posle start date-a

---

## Fokus na korisničko iskustvo

### Za ciljnu grupu (radnici manuelnih zanimanja):
1. **Jednostavni datumi** - dropdown umjesto calendar picker-a
2. **Veliki touch target-i** - button-i i input-i veći za mobilne
3. **Jasni primeri** - relevantni placeholder-i
4. **Vizuelne oznake** - badge-ovi, ikone, boje za status
5. **Progresivno popunjavanje** - korak po korak bez preopterećenja
6. **Opcija za neiskusne** - "Nemam radnog iskustva" checkbox

### Mobilna optimizacija:
- Touch-friendly element-i (minimum 44px)
- Kontrast i čitljivost
- Jednostavna navigacija
- Brzina učitavanja

---

**Rezultat:** Kompletan, intuitivan onboarding koji prikuplja sve potrebne informacije za job matching algoritam, prilagođen specifičnoj ciljnoj grupi radnika.