import 'package:flutter/material.dart';
import '../constants/design_constants.dart';

class TimelineItem {
  final String title;
  final String? subtitle;
  final String date;
  final String? description;
  final IconData icon;
  final bool isCompleted;
  final bool isActive;
  final Color? color;

  TimelineItem({
    required this.title,
    this.subtitle,
    required this.date,
    this.description,
    required this.icon,
    this.isCompleted = false,
    this.isActive = false,
    this.color,
  });
}

class CompactTimeline extends StatelessWidget {
  final List<TimelineItem> items;
  final double lineWidth;
  final double iconSize;
  final EdgeInsets? padding;

  const CompactTimeline({
    Key? key,
    required this.items,
    this.lineWidth = 2.0,
    this.iconSize = 20.0,
    this.padding,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: padding ?? EdgeInsets.all(DesignConstants.spaceL),
      child: Column(
        children: items.asMap().entries.map<Widget>((entry) {
          final index = entry.key;
          final item = entry.value;
          final isLast = index == items.length - 1;
          
          return _TimelineItemWidget(
            item: item,
            isLast: isLast,
            lineWidth: lineWidth,
            iconSize: iconSize,
          );
        }).toList(),
      ),
    );
  }
}

class _TimelineItemWidget extends StatelessWidget {
  final TimelineItem item;
  final bool isLast;
  final double lineWidth;
  final double iconSize;

  const _TimelineItemWidget({
    Key? key,
    required this.item,
    required this.isLast,
    required this.lineWidth,
    required this.iconSize,
  }) : super(key: key);

  Color get _getColor {
    if (item.color != null) return item.color!;
    if (item.isCompleted) return DesignConstants.successGreen;
    if (item.isActive) return DesignConstants.secondaryBlue;
    return DesignConstants.borderGray;
  }

  @override
  Widget build(BuildContext context) {
    return IntrinsicHeight(
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Timeline indicator - COMPACTED
          Column(
            children: [
              Container(
                width: 28, // Reduced from 32
                height: 28, // Reduced from 32
                decoration: BoxDecoration(
                  color: item.isCompleted || item.isActive 
                      ? _getColor 
                      : Colors.white,
                  shape: BoxShape.circle,
                  border: Border.all(
                    color: _getColor,
                    width: 2,
                  ),
                  boxShadow: item.isCompleted || item.isActive 
                      ? DesignConstants.shadowMedium 
                      : null,
                ),
                child: Icon(
                  item.isCompleted ? Icons.check_rounded : item.icon,
                  color: item.isCompleted || item.isActive 
                      ? Colors.white 
                      : _getColor,
                  size: iconSize - 4, // Reduced icon size
                ),
              ),
              if (!isLast)
                Container(
                  width: lineWidth,
                  height: 24, // Reduced from 40
                  color: item.isCompleted 
                      ? _getColor.withOpacity(0.3)
                      : DesignConstants.borderGray.withOpacity(0.5),
                  margin: EdgeInsets.symmetric(vertical: DesignConstants.spaceXS), // Reduced margin
                ),
            ],
          ),
          
          SizedBox(width: DesignConstants.spaceM), // Reduced from spaceL
          
          // Content - COMPACTED
          Expanded(
            child: Padding(
              padding: EdgeInsets.only(
                bottom: isLast ? 0 : DesignConstants.spaceM, // Reduced from spaceL
                top: 2, // Reduced from spaceXS
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              item.title,
                              style: TextStyle(
                                fontSize: 14, // Reduced from 15
                                fontWeight: FontWeight.w600,
                                color: DesignConstants.textPrimary,
                                height: 1.1, // Reduced line height
                              ),
                            ),
                            if (item.subtitle != null) ...[
                              SizedBox(height: 1),
                              Text(
                                item.subtitle!,
                                style: TextStyle(
                                  fontSize: 12, // Reduced from 13
                                  color: DesignConstants.textSecondary,
                                  height: 1.1,
                                ),
                              ),
                            ],
                          ],
                        ),
                      ),
                      Container(
                        padding: EdgeInsets.symmetric(
                          horizontal: DesignConstants.spaceS - 1, // Slightly reduced
                          vertical: DesignConstants.spaceXS / 2,
                        ),
                        decoration: BoxDecoration(
                          color: _getColor.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(DesignConstants.radiusSmall),
                        ),
                        child: Text(
                          item.date,
                          style: TextStyle(
                            fontSize: 10, // Reduced from 11
                            fontWeight: FontWeight.w500,
                            color: _getColor,
                          ),
                        ),
                      ),
                    ],
                  ),
                  
                  // Show description only for active items to save space
                  if (item.description != null && (item.isActive || item.isCompleted && item.color == DesignConstants.successGreen)) ...[
                    SizedBox(height: DesignConstants.spaceXS), // Reduced spacing
                    Text(
                      item.description!,
                      style: TextStyle(
                        fontSize: 12, // Reduced from 13
                        color: DesignConstants.textSecondary,
                        height: 1.2, // Reduced line height
                      ),
                      maxLines: 2, // Limit description to 2 lines
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}

// Pre-built timeline for applications
class ApplicationTimeline extends StatelessWidget {
  final String status;
  final String applicationDate;
  final Map<String, dynamic> application;

  const ApplicationTimeline({
    Key? key,
    required this.status,
    required this.applicationDate,
    required this.application,
  }) : super(key: key);

  List<TimelineItem> _buildTimelineItems() {
    List<TimelineItem> items = [
      TimelineItem(
        title: 'Prijava poslana',
        date: applicationDate,
        icon: Icons.send_rounded,
        isCompleted: true,
        description: 'Vaša prijava je uspešno poslana kompaniji.',
        color: DesignConstants.successGreen,
      ),
    ];

    switch (status) {
      case 'review':
        items.add(TimelineItem(
          title: 'Pregled u toku',
          date: _getEstimatedDate(1),
          icon: Icons.visibility_rounded,
          isActive: true,
          description: 'HR tim pregledaju vašu prijavu...',
          color: DesignConstants.accentOrange,
        ));
        break;
        
      case 'interview':
        items.add(TimelineItem(
          title: 'Poziv za intervju',
          date: _getEstimatedDate(2),
          icon: Icons.people_rounded,
          isCompleted: true,
          description: 'Kompanija je prihvatila vašu prijavu.',
        ));
        items.add(TimelineItem(
          title: 'Zakazivanje intervjua',
          date: _getEstimatedDate(5),
          icon: Icons.schedule_rounded,
          isActive: true,
          description: 'Dogovaranje termina za intervju...',
          color: DesignConstants.secondaryBlue,
        ));
        break;
        
      case 'contract':
        items.addAll([
          TimelineItem(
            title: 'Poziv za intervju',
            date: _getEstimatedDate(2),
            icon: Icons.people_rounded,
            isCompleted: true,
            description: 'Uspešno završen intervju.',
          ),
          TimelineItem(
            title: 'Ponuda ugovora',
            date: _getEstimatedDate(5),
            icon: Icons.description_rounded,
            isCompleted: true,
            description: 'Čestitamo! Dobili ste ponudu za posao.',
            color: DesignConstants.successGreen,
          ),
          TimelineItem(
            title: 'Čeka se odluka',
            date: 'U toku',
            icon: Icons.pending_actions_rounded,
            isActive: true,
            description: 'Razmislite o ponudi i donesete odluku.',
            color: DesignConstants.accentOrange,
          ),
        ]);
        break;
        
      case 'onboarding':
        items.addAll([
          TimelineItem(
            title: 'Poziv za intervju',
            date: _getEstimatedDate(2),
            icon: Icons.people_rounded,
            isCompleted: true,
            description: 'Uspešno završen intervju.',
          ),
          TimelineItem(
            title: 'Ponuda ugovora',
            date: _getEstimatedDate(5),
            icon: Icons.description_rounded,
            isCompleted: true,
            description: 'Čestitamo! Dobili ste ponudu za posao.',
            color: DesignConstants.successGreen,
          ),
          TimelineItem(
            title: 'Prihvaćen ugovor',
            date: _getEstimatedDate(8),
            icon: Icons.handshake_rounded,
            isCompleted: true,
            description: 'Ugovor je prihvaćen sa obe strane.',
            color: DesignConstants.successGreen,
          ),
          TimelineItem(
            title: 'Priprema za početak rada',
            date: _getEstimatedDate(12),
            icon: Icons.flag_rounded,
            isActive: true,
            description: 'Priprema dokumenata i dozvola za rad.',
            color: Color(0xFF7C3AED),
          ),
        ]);
        break;
        
      case 'initial_rejected':
        items.add(TimelineItem(
          title: 'Prijava odbijena',
          date: _getEstimatedDate(3),
          icon: Icons.cancel_rounded,
          isCompleted: true,
          description: 'Nažalost, vaša prijava nije prihvaćena.',
          color: DesignConstants.errorRed,
        ));
        break;
        
      case 'interview_rejected':
        items.addAll([
          TimelineItem(
            title: 'Poziv za intervju',
            date: _getEstimatedDate(2),
            icon: Icons.people_rounded,
            isCompleted: true,
          ),
          TimelineItem(
            title: 'Niste odabrani',
            date: _getEstimatedDate(5),
            icon: Icons.thumb_down_rounded,
            isCompleted: true,
            description: 'Nakon intervjua, odabran je drugi kandidat.',
            color: DesignConstants.errorRed,
          ),
        ]);
        break;
    }

    return items;
  }

  String _getEstimatedDate(int daysAfter) {
    final parts = applicationDate.split(' ');
    if (parts.length >= 3) {
      final day = int.tryParse(parts[0].replaceAll('.', '')) ?? 1;
      final month = parts[1];
      final year = parts[2];
      
      final estimatedDay = day + daysAfter;
      return '$estimatedDay. $month $year';
    }
    return applicationDate;
  }

  @override
  Widget build(BuildContext context) {
    return CompactTimeline(
      items: _buildTimelineItems(),
      padding: EdgeInsets.all(DesignConstants.spaceXL),
    );
  }
}