import 'package:flutter/material.dart';
import '../utils/navigation_service.dart';

class InvitedJobDetailsScreen extends StatefulWidget {
  final Map<String, dynamic> job;
  final Map<String, dynamic> invitation;

  const InvitedJobDetailsScreen({
    Key? key,
    required this.job,
    required this.invitation,
  }) : super(key: key);

  @override
  _InvitedJobDetailsScreenState createState() => _InvitedJobDetailsScreenState();
}

class _InvitedJobDetailsScreenState extends State<InvitedJobDetailsScreen> {
  @override
  Widget build(BuildContext context) {
    return Material(
      color: const Color(0xFFFAFBFC),
      child: Stack(
        children: [
          Column(
            children: [
              _buildInvitationStatusBar(),
              Expanded(
                child: SingleChildScrollView(
                  padding: EdgeInsets.only(bottom: 100),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildJobHeaderCard(),
                      _buildJobDescription(),
                      _buildRequirementsSection(),
                      _buildBenefitsSection(),
                      SizedBox(height: 80), // Space for floating button
                    ],
                  ),
                ),
              ),
            ],
          ),
          // Floating Action Buttons
          Positioned(
            left: 20,
            right: 20,
            bottom: 20,
            child: SafeArea(
              child: _buildInvitationActionButtons(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInvitationStatusBar() {
    return Container(
      color: const Color(0xFF3B82F6).withOpacity(0.1),
      child: Container(
        width: double.infinity,
        padding: EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: const Color(0xFF3B82F6).withOpacity(0.1),
          border: Border(
            bottom: BorderSide(
              color: const Color(0xFF3B82F6).withOpacity(0.3),
              width: 1,
            ),
          ),
        ),
        child: Row(
          children: [
            Container(
              padding: EdgeInsets.all(6),
              decoration: BoxDecoration(
                color: const Color(0xFF3B82F6),
                shape: BoxShape.circle,
              ),
              child: Icon(
                Icons.mail_outline,
                color: Colors.white,
                size: 16,
              ),
            ),
            SizedBox(width: 12),
            Expanded(
              child: Text(
                'Kompanija Vas je pozvala da se prijavite na ovaj posao',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: const Color(0xFF3B82F6),
                ),
              ),
            ),
            Text(
              widget.invitation['receivedDate'] ?? 'Danas',
              style: TextStyle(
                fontSize: 12,
                color: const Color(0xFF3B82F6).withOpacity(0.8),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildJobHeaderCard() {
    return Container(
      margin: EdgeInsets.fromLTRB(20, 16, 20, 0),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: const Color(0xFFE2E8F0),
          width: 1,
        ),
      ),
      child: Container(
        decoration: BoxDecoration(
          color: const Color(0xFF1E3A8A).withOpacity(0.02),
          borderRadius: BorderRadius.circular(16),
        ),
        padding: EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        widget.job['title'] ?? 'Frontend Developer',
                        style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: const Color(0xFF0A0A0A),
                          height: 1.2,
                        ),
                      ),
                      SizedBox(height: 4),
                      Text(
                        widget.job['company'] ?? 'WebSolutions',
                        style: TextStyle(
                          color: const Color(0xFF64748B),
                          fontWeight: FontWeight.w500,
                          fontSize: 16,
                        ),
                      ),
                      SizedBox(height: 8),
                      Row(
                        children: [
                          Text(
                            widget.job['countryFlag'] ?? '🇩🇪',
                            style: TextStyle(fontSize: 18),
                          ),
                          SizedBox(width: 8),
                          Text(
                            '${widget.job['city'] ?? 'Hamburg'}, ${widget.job['country'] ?? 'Njemačka'}',
                            style: TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.w500,
                              color: const Color(0xFF0A0A0A),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                SizedBox(width: 12),
                Text(
                  widget.job['logo'] ?? '🏢',
                  style: TextStyle(fontSize: 48),
                ),
              ],
            ),
            SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  widget.job['salary'] ?? '€3,000 - €4,200',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: const Color(0xFF1E3A8A),
                  ),
                ),
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: const Color(0xFFF1F5F9),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    widget.job['type'] ?? 'Puno radno vrijeme',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      color: const Color(0xFF475569),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildJobDescription() {
    return Container(
      margin: EdgeInsets.fromLTRB(20, 24, 20, 0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Opis posla',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: const Color(0xFF0F172A),
            ),
          ),
          SizedBox(height: 12),
          Container(
            width: double.infinity,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: const Color(0xFFE2E8F0),
                width: 1,
              ),
            ),
            padding: EdgeInsets.all(20),
            child: Text(
              widget.job['description'] ?? 'Pridružite se našem dinamičnom timu kao Frontend Developer i radite na inovativnim web projektima. Tražimo kreativnu osobu koja će pomoći u razvoju modernih korisničkih interfejsa koristeći najnovije tehnologije. Ova pozicija nudi odličnu priliku za profesionalni rast u međunarodnom okruženju.',
              style: TextStyle(
                fontSize: 15,
                color: const Color(0xFF0F172A),
                height: 1.6,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRequirementsSection() {
    final requirements = widget.job['requirements'] ?? [
      'Njemački jezik - nivo B2 ili viši',
      'Vozačka dozvola kategorije B',
      'Minimum 2 godine iskustva u React.js',
      'Poznavanje TypeScript-a',
      'Iskustvo sa Tailwind CSS',
      'Razumijevanje Git workflow-a',
      'Komunikacijske vještine'
    ];
    
    return Container(
      margin: EdgeInsets.fromLTRB(20, 24, 20, 0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Zahtjevi',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: const Color(0xFF0F172A),
            ),
          ),
          SizedBox(height: 12),
          Container(
            width: double.infinity,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: const Color(0xFFE2E8F0),
                width: 1,
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.05),
                  blurRadius: 4,
                  offset: Offset(0, 1),
                ),
              ],
            ),
            padding: EdgeInsets.all(20),
            child: Column(
              children: requirements.asMap().entries.map<Widget>((entry) {
                int index = entry.key;
                String requirement = entry.value;
                IconData icon = _getRequirementIcon(index);
                
                return Padding(
                  padding: EdgeInsets.only(bottom: index < requirements.length - 1 ? 12 : 0),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Container(
                        margin: EdgeInsets.only(top: 2),
                        child: Icon(
                          icon,
                          size: 16,
                          color: const Color(0xFF1E3A8A),
                        ),
                      ),
                      SizedBox(width: 12),
                      Expanded(
                        child: Text(
                          requirement,
                          style: TextStyle(
                            fontSize: 14,
                            color: const Color(0xFF0A0A0A),
                          ),
                        ),
                      ),
                    ],
                  ),
                );
              }).toList(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBenefitsSection() {
    final benefits = widget.job['benefits'] ?? [
      'Fleksibilno radno vrijeme',
      'Mogućnost rada od kuće',
      'Privatno zdravstveno osiguranje',
      'Godišnji bonus',
      'Besplatna kafa i grickalice',
      'Tim building eventi',
      'Mogućnost napredovanja',
      'Moderni radni prostor'
    ];
    
    return Container(
      margin: EdgeInsets.fromLTRB(20, 24, 20, 0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Benefiti',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: const Color(0xFF0F172A),
            ),
          ),
          SizedBox(height: 12),
          Container(
            width: double.infinity,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: const Color(0xFFE2E8F0),
                width: 1,
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.05),
                  blurRadius: 4,
                  offset: Offset(0, 1),
                ),
              ],
            ),
            padding: EdgeInsets.all(20),
            child: Column(
              children: benefits.asMap().entries.map<Widget>((entry) {
                int index = entry.key;
                String benefit = entry.value;
                IconData icon = _getBenefitIcon(index);
                
                return Padding(
                  padding: EdgeInsets.only(bottom: index < benefits.length - 1 ? 12 : 0),
                  child: Row(
                    children: [
                      Container(
                        margin: EdgeInsets.only(top: 2),
                        child: Icon(
                          icon,
                          size: 16,
                          color: const Color(0xFF059669),
                        ),
                      ),
                      SizedBox(width: 12),
                      Expanded(
                        child: Text(
                          benefit,
                          style: TextStyle(
                            fontSize: 14,
                            color: const Color(0xFF0A0A0A),
                          ),
                        ),
                      ),
                    ],
                  ),
                );
              }).toList(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInvitationActionButtons() {
    return Row(
      children: [
        // Decline Button
        Expanded(
          child: Container(
            decoration: BoxDecoration(
              boxShadow: [
                BoxShadow(
                  color: const Color(0xFFEF4444).withOpacity(0.3),
                  blurRadius: 20,
                  offset: Offset(0, 8),
                ),
              ],
            ),
            child: OutlinedButton(
              onPressed: () {
                _showDeclineDialog();
              },
              style: OutlinedButton.styleFrom(
                backgroundColor: Colors.white,
                side: BorderSide(color: const Color(0xFFEF4444), width: 2),
                elevation: 0,
                shadowColor: Colors.transparent,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(16),
                ),
                padding: EdgeInsets.symmetric(vertical: 16),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.close_rounded,
                    size: 20,
                    color: const Color(0xFFEF4444),
                  ),
                  SizedBox(width: 8),
                  Text(
                    'Odbij',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: const Color(0xFFEF4444),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
        SizedBox(width: 16),
        // Accept Button
        Expanded(
          child: Container(
            decoration: BoxDecoration(
              boxShadow: [
                BoxShadow(
                  color: const Color(0xFF10B981).withOpacity(0.3),
                  blurRadius: 20,
                  offset: Offset(0, 8),
                ),
              ],
            ),
            child: ElevatedButton(
              onPressed: () {
                _showAcceptDialog();
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF10B981),
                foregroundColor: Colors.white,
                elevation: 0,
                shadowColor: Colors.transparent,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(16),
                ),
                padding: EdgeInsets.symmetric(vertical: 16),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.check_rounded,
                    size: 20,
                  ),
                  SizedBox(width: 8),
                  Text(
                    'Prijavi se',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  IconData _getRequirementIcon(int index) {
    switch (index) {
      case 0:
        return Icons.language;
      case 1:
        return Icons.directions_car;
      default:
        return Icons.check_circle_outline;
    }
  }

  IconData _getBenefitIcon(int index) {
    switch (index) {
      case 0:
        return Icons.access_time;
      case 1:
        return Icons.business;
      case 2:
        return Icons.security;
      case 3:
        return Icons.euro;
      case 4:
        return Icons.coffee;
      case 5:
        return Icons.people;
      case 6:
        return Icons.trending_up;
      case 7:
        return Icons.favorite;
      default:
        return Icons.check_circle_outline;
    }
  }

  void _showAcceptDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        title: Row(
          children: [
            Container(
              padding: EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: const Color(0xFF10B981).withOpacity(0.1),
                shape: BoxShape.circle,
              ),
              child: Icon(
                Icons.check_rounded,
                color: const Color(0xFF10B981),
                size: 24,
              ),
            ),
            SizedBox(width: 12),
            Text(
              'Prijavi se',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: const Color(0xFF0F172A),
              ),
            ),
          ],
        ),
        content: Text(
          'Da li ste sigurni da se želite prijaviti za poziciju "${widget.job['title']}" u kompaniji ${widget.job['company']}?',
          style: TextStyle(
            fontSize: 16,
            color: const Color(0xFF64748B),
            height: 1.5,
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'Otkaži',
              style: TextStyle(
                color: const Color(0xFF64748B),
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              NavigationService().navigateBack();
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Row(
                    children: [
                      Icon(Icons.check_circle, color: Colors.white),
                      SizedBox(width: 12),
                      Text('Uspešno ste se prijavili na posao!'),
                    ],
                  ),
                  backgroundColor: const Color(0xFF10B981),
                  behavior: SnackBarBehavior.floating,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF10B981),
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: Text(
              'Prijavi se',
              style: TextStyle(fontWeight: FontWeight.w600),
            ),
          ),
        ],
      ),
    );
  }

  void _showDeclineDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        title: Row(
          children: [
            Container(
              padding: EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: const Color(0xFFEF4444).withOpacity(0.1),
                shape: BoxShape.circle,
              ),
              child: Icon(
                Icons.close_rounded,
                color: const Color(0xFFEF4444),
                size: 24,
              ),
            ),
            SizedBox(width: 12),
            Text(
              'Odbij pozivnicu',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: const Color(0xFF0F172A),
              ),
            ),
          ],
        ),
        content: Text(
          'Da li ste sigurni da želite da odbijete pozivnicu za poziciju "${widget.job['title']}" u kompaniji ${widget.job['company']}?\n\nOva akcija se ne može poništiti.',
          style: TextStyle(
            fontSize: 16,
            color: const Color(0xFF64748B),
            height: 1.5,
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'Otkaži',
              style: TextStyle(
                color: const Color(0xFF64748B),
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              NavigationService().navigateBack();
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Row(
                    children: [
                      Icon(Icons.info_outline, color: Colors.white),
                      SizedBox(width: 12),
                      Text('Pozivnica je odbijena.'),
                    ],
                  ),
                  backgroundColor: const Color(0xFF64748B),
                  behavior: SnackBarBehavior.floating,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFFEF4444),
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: Text(
              'Odbij',
              style: TextStyle(fontWeight: FontWeight.w600),
            ),
          ),
        ],
      ),
    );
  }
}