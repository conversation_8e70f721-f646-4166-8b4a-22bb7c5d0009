import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'main_screen.dart';

class WorkExperience {
  String? position;
  String? company;
  String? location;
  int? startMonth;
  int? startYear;
  int? endMonth;
  int? endYear;
  bool isCurrentJob = false;

  final TextEditingController positionController = TextEditingController();
  final TextEditingController companyController = TextEditingController();
  final TextEditingController locationController = TextEditingController();

  WorkExperience({
    this.position,
    this.company,
    this.location,
    this.startMonth,
    this.startYear,
    this.endMonth,
    this.endYear,
    this.isCurrentJob = false,
  }) {
    positionController.text = position ?? '';
    companyController.text = company ?? '';
    locationController.text = location ?? '';
  }

  void dispose() {
    positionController.dispose();
    companyController.dispose();
    locationController.dispose();
  }
}

class OnboardingScreen extends StatefulWidget {
  final String firstName;
  final String lastName;
  final String phoneNumber;
  final int? startFromStep;
  final bool isFromProfile;

  OnboardingScreen({
    required this.firstName,
    required this.lastName,
    required this.phoneNumber,
    this.startFromStep,
    this.isFromProfile = false,
  });

  @override
  _OnboardingScreenState createState() => _OnboardingScreenState();
}

class _OnboardingScreenState extends State<OnboardingScreen> {
  int currentStep = 1;
  final int totalSteps = 6;

  // Step 1: Country selection
  String? selectedCountry;
  final Map<String, Map<String, String>> countries = {
    'BA': {'name': 'Bosnia and Herzegovina', 'flag': '🇧🇦'},
    'HR': {'name': 'Croatia', 'flag': '🇭🇷'},
    'RS': {'name': 'Serbia', 'flag': '🇷🇸'},
    'ME': {'name': 'Montenegro', 'flag': '🇲🇪'},
    'MK': {'name': 'North Macedonia', 'flag': '🇲🇰'},
    'SI': {'name': 'Slovenia', 'flag': '🇸🇮'},
    'AL': {'name': 'Albania', 'flag': '🇦🇱'},
    'DE': {'name': 'Germany', 'flag': '🇩🇪'},
    'AT': {'name': 'Austria', 'flag': '🇦🇹'},
    'CH': {'name': 'Switzerland', 'flag': '🇨🇭'},
  };

  // Step 2: Location
  final TextEditingController locationController = TextEditingController();

  // Step 3: Driving licenses
  final Set<String> selectedLicenses = {};
  final List<String> licenseCategories = ['A', 'B', 'C', 'CE', 'D'];

  // Step 4: Language proficiency
  String? germanLevel;
  String? englishLevel;
  final Map<String, String> languageLevels = {
    'none': 'Bez znanja',
    'a1': 'A1 - Početno znanje',
    'a2': 'A2 - Osnovno znanje',
    'b1': 'B1 - Srednje znanje',
    'b2': 'B2 - Više srednje znanje',
    'c1': 'C1 - Napredno znanje',
    'c2': 'C2 - Tečno/Maternji jezik',
  };

  // Step 5: Education
  String? educationLevel;
  final TextEditingController fieldOfStudyController = TextEditingController();
  final TextEditingController schoolNameController = TextEditingController();
  final TextEditingController schoolLocationController = TextEditingController();
  int? educationStartMonth;
  int? educationStartYear;
  int? educationEndMonth;
  int? educationEndYear;
  final Map<String, String> educationLevels = {
    'osnovna': 'Osnovna škola',
    'srednja': 'Srednja škola',
    'visa': 'Viša škola',
    'fakultet': 'Fakultet',
    'master': 'Master',
    'doktorat': 'Doktorat',
  };

  // Step 6: Work experience
  List<WorkExperience> workExperiences = [];
  bool hasNoWorkExperience = false;

  @override
  void initState() {
    super.initState();
    // Set starting step if provided
    if (widget.startFromStep != null) {
      currentStep = widget.startFromStep!;
    }
    // Add listener to location controller to update UI when text changes
    locationController.addListener(() {
      setState(() {});
    });
  }

  void _nextStep() {
    if (currentStep < totalSteps) {
      setState(() {
        currentStep++;
      });
    } else {
      // Complete onboarding
      if (widget.isFromProfile) {
        // Return to profile screen with success
        Navigator.pop(context, true); // true indicates success
      } else {
        // Normal onboarding flow
        Navigator.pushReplacement(
          context,
          MaterialPageRoute(builder: (context) => MainScreen()),
        );
      }
    }
  }

  void _showSkipModal() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(28),
          ),
          backgroundColor: Colors.transparent,
          child: Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  const Color(0xFF2665D0),
                  const Color(0xFF1E4DB7),
                  const Color(0xFF1A3F8C),
                ],
                stops: [0.0, 0.6, 1.0],
              ),
              borderRadius: BorderRadius.circular(28),
              border: Border.all(
                color: Colors.white.withOpacity(0.2),
                width: 1,
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.3),
                  blurRadius: 30,
                  offset: Offset(0, 15),
                ),
              ],
            ),
            padding: EdgeInsets.all(36),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  width: 90,
                  height: 90,
                  decoration: BoxDecoration(
                    color: const Color(0xFFFBBF24).withOpacity(0.2),
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: const Color(0xFFFBBF24).withOpacity(0.4),
                      width: 2,
                    ),
                  ),
                  child: Icon(
                    Icons.warning_rounded,
                    size: 45,
                    color: const Color(0xFFFBBF24),
                  ),
                ),
                SizedBox(height: 28),
                ShaderMask(
                  shaderCallback: (bounds) => LinearGradient(
                    colors: [Colors.white, Colors.white.withOpacity(0.9)],
                  ).createShader(bounds),
                  child: Text(
                    'Preskočiti kreiranje profila?',
                    style: GoogleFonts.outfit(
                      fontSize: 24,
                      fontWeight: FontWeight.w800,
                      color: Colors.white,
                      letterSpacing: -1,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
                SizedBox(height: 20),
                Text(
                  'Nećete se moći prijaviti na poslove dok ne ispunite profil do kraja. Poslodavci neće vidjeti vaš profil među ostalim profilima i neće vam moći poslati pozivnice za njihove poslove.\n\nJeste li sigurni da želite preskočiti kreiranje profila?',
                  textAlign: TextAlign.center,
                  style: GoogleFonts.inter(
                    fontSize: 15,
                    color: Colors.white.withOpacity(0.9),
                    height: 1.5,
                    fontWeight: FontWeight.w400,
                  ),
                ),
                SizedBox(height: 36),
                Row(
                  children: [
                    Expanded(
                      child: Container(
                        height: 56,
                        decoration: BoxDecoration(
                          color: Colors.white.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(18),
                          border: Border.all(
                            color: Colors.white.withOpacity(0.3),
                            width: 1,
                          ),
                        ),
                        child: TextButton(
                          onPressed: () {
                            Navigator.of(context).pop();
                            Navigator.pushReplacement(
                              context,
                              MaterialPageRoute(builder: (context) => MainScreen()),
                            );
                          },
                          style: TextButton.styleFrom(
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(18),
                            ),
                          ),
                          child: Text(
                            'Da, preskoči',
                            style: GoogleFonts.inter(
                              fontSize: 14,
                              color: Colors.white.withOpacity(0.9),
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                      ),
                    ),
                    SizedBox(width: 16),
                    Expanded(
                      child: Container(
                        height: 56,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(18),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withOpacity(0.15),
                              blurRadius: 12,
                              offset: Offset(0, 4),
                            ),
                          ],
                        ),
                        child: ElevatedButton(
                          onPressed: () {
                            Navigator.of(context).pop();
                          },
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.white,
                            foregroundColor: const Color(0xFF2665D0),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(18),
                            ),
                            elevation: 0,
                            shadowColor: Colors.transparent,
                          ),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Flexible(
                                child: Text(
                                  'Nastavi',
                                  style: GoogleFonts.outfit(
                                    fontSize: 14,
                                    fontWeight: FontWeight.w700,
                                  ),
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                              SizedBox(width: 4),
                              Icon(
                                Icons.arrow_forward_rounded,
                                size: 16,
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildProgressBar() {
    final progress = currentStep / totalSteps;
    return Container(
      padding: EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.1),
        border: Border(
          bottom: BorderSide(
            color: Colors.white.withOpacity(0.2),
            width: 1,
          ),
        ),
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Korak $currentStep od $totalSteps',
                style: GoogleFonts.inter(
                  fontSize: 16,
                  color: Colors.white.withOpacity(0.9),
                  fontWeight: FontWeight.w500,
                ),
              ),
              Container(
                padding: EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: Colors.white.withOpacity(0.3),
                    width: 1,
                  ),
                ),
                child: Text(
                  '${(progress * 100).round()}%',
                  style: GoogleFonts.inter(
                    fontSize: 14,
                    color: Colors.white,
                    fontWeight: FontWeight.w700,
                  ),
                ),
              ),
            ],
          ),
          SizedBox(height: 16),
          Container(
            height: 8,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(4),
              color: Colors.white.withOpacity(0.2),
            ),
            child: LinearProgressIndicator(
              value: progress,
              backgroundColor: Colors.transparent,
              valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
              minHeight: 8,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStep1() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Iz koje ste države?',
          style: GoogleFonts.inter(
            fontSize: 24,
            fontWeight: FontWeight.w700,
            color: const Color(0xFF0F172A),
          ),
        ),
        SizedBox(height: 8),
        Text(
          'Izaberite zemlju iz koje dolazite',
          style: GoogleFonts.inter(
            fontSize: 16,
            color: const Color(0xFF64748B),
          ),
        ),
        SizedBox(height: 32),
        Container(
          width: double.infinity,
          padding: EdgeInsets.symmetric(horizontal: 16),
          decoration: BoxDecoration(
            border: Border.all(color: const Color(0xFFE2E8F0)),
            borderRadius: BorderRadius.circular(12),
            color: const Color(0xFFFAFBFC),
          ),
          child: DropdownButton<String>(
            value: selectedCountry,
            isExpanded: true,
            underline: SizedBox(),
            icon: Icon(Icons.arrow_drop_down, color: const Color(0xFF64748B)),
            hint: Text(
              'Izaberite državu',
              style: GoogleFonts.inter(
                fontSize: 16,
                color: const Color(0xFF64748B),
              ),
            ),
            items: countries.entries.map((entry) {
              return DropdownMenuItem<String>(
                value: entry.key,
                child: Row(
                  children: [
                    Text(
                      entry.value['flag']!,
                      style: TextStyle(fontSize: 24),
                    ),
                    SizedBox(width: 12),
                    Text(
                      entry.value['name']!,
                      style: GoogleFonts.inter(
                        fontSize: 16,
                        color: const Color(0xFF0F172A),
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              );
            }).toList(),
            onChanged: (String? newValue) {
              setState(() {
                selectedCountry = newValue;
              });
            },
          ),
        ),
      ],
    );
  }

  Widget _buildStep2() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Iz kojeg ste mjesta?',
          style: GoogleFonts.inter(
            fontSize: 24,
            fontWeight: FontWeight.w700,
            color: const Color(0xFF0F172A),
          ),
        ),
        SizedBox(height: 8),
        Text(
          'Unesite naziv mjesta odakle dolazite',
          style: GoogleFonts.inter(
            fontSize: 16,
            color: const Color(0xFF64748B),
          ),
        ),
        SizedBox(height: 32),
        TextField(
          controller: locationController,
          decoration: InputDecoration(
            labelText: 'Mjesto',
            hintText: 'Unesite mjesto',
            labelStyle: TextStyle(
              color: const Color(0xFF64748B),
              fontWeight: FontWeight.w500,
            ),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: const Color(0xFFE2E8F0)),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: const Color(0xFFE2E8F0)),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: const Color(0xFF1E3A8A), width: 2),
            ),
            filled: true,
            fillColor: const Color(0xFFFAFBFC),
          ),
        ),
      ],
    );
  }

  Widget _buildStep3() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Vozačka dozvola',
          style: GoogleFonts.inter(
            fontSize: 24,
            fontWeight: FontWeight.w700,
            color: const Color(0xFF0F172A),
          ),
        ),
        SizedBox(height: 8),
        Text(
          'Odaberite kategorije vozačke dozvole koje posjedujete',
          style: GoogleFonts.inter(
            fontSize: 16,
            color: const Color(0xFF64748B),
          ),
        ),
        SizedBox(height: 32),
        Wrap(
          spacing: 12,
          runSpacing: 12,
          children: licenseCategories.map((category) {
            final isSelected = selectedLicenses.contains(category);
            return GestureDetector(
              onTap: () {
                setState(() {
                  if (isSelected) {
                    selectedLicenses.remove(category);
                  } else {
                    selectedLicenses.add(category);
                  }
                });
              },
              child: Container(
                width: 80,
                height: 80,
                decoration: BoxDecoration(
                  color: isSelected ? const Color(0xFF1E3A8A) : Colors.white,
                  border: Border.all(
                    color: isSelected ? const Color(0xFF1E3A8A) : const Color(0xFFE2E8F0),
                    width: 2,
                  ),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Center(
                  child: Text(
                    category,
                    style: GoogleFonts.inter(
                      fontSize: 18,
                      fontWeight: FontWeight.w700,
                      color: isSelected ? Colors.white : const Color(0xFF64748B),
                    ),
                  ),
                ),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildStep4() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Jezičko znanje',
          style: GoogleFonts.inter(
            fontSize: 24,
            fontWeight: FontWeight.w700,
            color: const Color(0xFF0F172A),
          ),
        ),
        SizedBox(height: 8),
        Text(
          'Odaberite nivo poznavanja jezika',
          style: GoogleFonts.inter(
            fontSize: 16,
            color: const Color(0xFF64748B),
          ),
        ),
        SizedBox(height: 32),
        
        // German Language Section
        Text(
          'Njemački jezik',
          style: GoogleFonts.inter(
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: const Color(0xFF0F172A),
          ),
        ),
        SizedBox(height: 12),
        Container(
          width: double.infinity,
          padding: EdgeInsets.symmetric(horizontal: 16),
          decoration: BoxDecoration(
            border: Border.all(color: const Color(0xFFE2E8F0)),
            borderRadius: BorderRadius.circular(12),
            color: const Color(0xFFFAFBFC),
          ),
          child: DropdownButton<String>(
            value: germanLevel,
            isExpanded: true,
            underline: SizedBox(),
            icon: Icon(Icons.arrow_drop_down, color: const Color(0xFF64748B)),
            hint: Text(
              'Izaberite nivo poznavanja njemačkog',
              style: GoogleFonts.inter(
                fontSize: 16,
                color: const Color(0xFF64748B),
              ),
            ),
            items: languageLevels.entries.map((entry) {
              return DropdownMenuItem<String>(
                value: entry.key,
                child: Text(
                  entry.value,
                  style: GoogleFonts.inter(
                    fontSize: 16,
                    color: const Color(0xFF0F172A),
                    fontWeight: FontWeight.w500,
                  ),
                ),
              );
            }).toList(),
            onChanged: (String? newValue) {
              setState(() {
                germanLevel = newValue;
              });
            },
          ),
        ),
        
        SizedBox(height: 24),
        
        // English Language Section
        Text(
          'Engleski jezik',
          style: GoogleFonts.inter(
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: const Color(0xFF0F172A),
          ),
        ),
        SizedBox(height: 12),
        Container(
          width: double.infinity,
          padding: EdgeInsets.symmetric(horizontal: 16),
          decoration: BoxDecoration(
            border: Border.all(color: const Color(0xFFE2E8F0)),
            borderRadius: BorderRadius.circular(12),
            color: const Color(0xFFFAFBFC),
          ),
          child: DropdownButton<String>(
            value: englishLevel,
            isExpanded: true,
            underline: SizedBox(),
            icon: Icon(Icons.arrow_drop_down, color: const Color(0xFF64748B)),
            hint: Text(
              'Izaberite nivo poznavanja engleskog',
              style: GoogleFonts.inter(
                fontSize: 16,
                color: const Color(0xFF64748B),
              ),
            ),
            items: languageLevels.entries.map((entry) {
              return DropdownMenuItem<String>(
                value: entry.key,
                child: Text(
                  entry.value,
                  style: GoogleFonts.inter(
                    fontSize: 16,
                    color: const Color(0xFF0F172A),
                    fontWeight: FontWeight.w500,
                  ),
                ),
              );
            }).toList(),
            onChanged: (String? newValue) {
              setState(() {
                englishLevel = newValue;
              });
            },
          ),
        ),
      ],
    );
  }

  Widget _buildStep5() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Obrazovanje',
          style: GoogleFonts.inter(
            fontSize: 24,
            fontWeight: FontWeight.w700,
            color: const Color(0xFF0F172A),
          ),
        ),
        SizedBox(height: 8),
        Text(
          'Unesite informacije o vašem obrazovanju',
          style: GoogleFonts.inter(
            fontSize: 16,
            color: const Color(0xFF64748B),
          ),
        ),
        SizedBox(height: 32),
        
        // Nivo obrazovanja
        Text(
          'Nivo obrazovanja',
          style: GoogleFonts.inter(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: const Color(0xFF0F172A),
          ),
        ),
        SizedBox(height: 12),
        Container(
          width: double.infinity,
          padding: EdgeInsets.symmetric(horizontal: 16),
          decoration: BoxDecoration(
            border: Border.all(color: const Color(0xFFE2E8F0)),
            borderRadius: BorderRadius.circular(12),
            color: const Color(0xFFFAFBFC),
          ),
          child: DropdownButton<String>(
            value: educationLevel,
            isExpanded: true,
            underline: SizedBox(),
            icon: Icon(Icons.arrow_drop_down, color: const Color(0xFF64748B)),
            hint: Text(
              'Izaberite nivo obrazovanja',
              style: GoogleFonts.inter(
                fontSize: 16,
                color: const Color(0xFF64748B),
              ),
            ),
            items: educationLevels.entries.map((entry) {
              return DropdownMenuItem<String>(
                value: entry.key,
                child: Text(
                  entry.value,
                  style: GoogleFonts.inter(
                    fontSize: 16,
                    color: const Color(0xFF0F172A),
                    fontWeight: FontWeight.w500,
                  ),
                ),
              );
            }).toList(),
            onChanged: (String? newValue) {
              setState(() {
                educationLevel = newValue;
              });
            },
          ),
        ),
        
        SizedBox(height: 24),
        
        // Smjer/Zvanje
        Text(
          'Smjer/Zvanje',
          style: GoogleFonts.inter(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: const Color(0xFF0F172A),
          ),
        ),
        SizedBox(height: 12),
        TextField(
          controller: fieldOfStudyController,
          decoration: InputDecoration(
            hintText: 'Npr. Elektrotehnika, Mašinstvo, Automehanicar...',
            hintStyle: TextStyle(color: const Color(0xFF94A3B8)),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: const Color(0xFFE2E8F0)),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: const Color(0xFFE2E8F0)),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: const Color(0xFF1E3A8A), width: 2),
            ),
            filled: true,
            fillColor: const Color(0xFFFAFBFC),
          ),
        ),
        
        SizedBox(height: 24),
        
        // Mjesto
        Text(
          'Mjesto',
          style: GoogleFonts.inter(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: const Color(0xFF0F172A),
          ),
        ),
        SizedBox(height: 12),
        TextField(
          controller: schoolLocationController,
          decoration: InputDecoration(
            hintText: 'Unesite mjesto gdje ste išli u školu',
            hintStyle: TextStyle(color: const Color(0xFF94A3B8)),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: const Color(0xFFE2E8F0)),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: const Color(0xFFE2E8F0)),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: const Color(0xFF1E3A8A), width: 2),
            ),
            filled: true,
            fillColor: const Color(0xFFFAFBFC),
          ),
        ),
        
        SizedBox(height: 24),
        
        // Period obrazovanja - Od kada
        Text(
          'Od kada',
          style: GoogleFonts.inter(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: const Color(0xFF0F172A),
          ),
        ),
        SizedBox(height: 12),
        _buildMonthYearPicker(
          'Od',
          educationStartMonth,
          educationStartYear,
          (month, year) {
            setState(() {
              educationStartMonth = month;
              educationStartYear = year;
            });
          },
        ),
        
        SizedBox(height: 24),
        
        // Period obrazovanja - Do kada
        Text(
          'Do kada',
          style: GoogleFonts.inter(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: const Color(0xFF0F172A),
          ),
        ),
        SizedBox(height: 12),
        _buildMonthYearPicker(
          'Do',
          educationEndMonth,
          educationEndYear,
          (month, year) {
            setState(() {
              educationEndMonth = month;
              educationEndYear = year;
            });
          },
        ),
      ],
    );
  }

  Widget _buildStep6() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Radno iskustvo',
          style: GoogleFonts.inter(
            fontSize: 24,
            fontWeight: FontWeight.w700,
            color: const Color(0xFF0F172A),
          ),
        ),
        SizedBox(height: 8),
        Text(
          'Dodajte vaše radno iskustvo',
          style: GoogleFonts.inter(
            fontSize: 16,
            color: const Color(0xFF64748B),
          ),
        ),
        SizedBox(height: 24),
        
        // Checkbox za "Nemam radno iskustvo"
        Container(
          padding: EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: const Color(0xFFF8FAFC),
            border: Border.all(color: const Color(0xFFE2E8F0)),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Row(
            children: [
              Checkbox(
                value: hasNoWorkExperience,
                onChanged: (bool? value) {
                  setState(() {
                    hasNoWorkExperience = value ?? false;
                    if (hasNoWorkExperience) {
                      for (var experience in workExperiences) {
                        experience.dispose();
                      }
                      workExperiences.clear();
                    }
                  });
                },
                activeColor: const Color(0xFF1E3A8A),
              ),
              SizedBox(width: 12),
              Expanded(
                child: Text(
                  'Nemam radno iskustvo',
                  style: GoogleFonts.inter(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                    color: const Color(0xFF0F172A),
                  ),
                ),
              ),
            ],
          ),
        ),
        
        if (!hasNoWorkExperience) ...[
          SizedBox(height: 24),
          
          // Lista radnih iskustava
          ...workExperiences.asMap().entries.map((entry) {
            int index = entry.key;
            WorkExperience experience = entry.value;
            return Container(
              margin: EdgeInsets.only(bottom: 16),
              child: _buildWorkExperienceCard(experience, index),
            );
          }).toList(),
          
          // Button za dodavanje novog iskustva
          Container(
            width: double.infinity,
            child: OutlinedButton.icon(
              onPressed: () {
                setState(() {
                  workExperiences.add(WorkExperience());
                });
              },
              icon: Icon(
                Icons.add,
                color: const Color(0xFF1E3A8A),
              ),
              label: Text(
                workExperiences.isEmpty ? 'Dodaj radno iskustvo' : 'Dodaj još jedno iskustvo',
                style: GoogleFonts.inter(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: const Color(0xFF1E3A8A),
                ),
              ),
              style: OutlinedButton.styleFrom(
                padding: EdgeInsets.symmetric(vertical: 16),
                side: BorderSide(color: const Color(0xFF1E3A8A), width: 2),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildMonthYearPicker(
    String label,
    int? selectedMonth,
    int? selectedYear,
    Function(int?, int?) onChanged,
  ) {
    final months = [
      'Jan', 'Feb', 'Mar', 'Apr', 'Maj', 'Jun',
      'Jul', 'Aug', 'Sep', 'Okt', 'Nov', 'Dec'
    ];
    
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        border: Border.all(color: const Color(0xFFE2E8F0)),
        borderRadius: BorderRadius.circular(8),
        color: const Color(0xFFFAFBFC),
      ),
      child: Row(
        children: [
          // Month dropdown
          Expanded(
            flex: 2,
            child: DropdownButton<int>(
              value: selectedMonth,
              isExpanded: true,
              underline: SizedBox(),
              hint: Text(
                'Mjesec',
                style: GoogleFonts.inter(
                  fontSize: 14,
                  color: const Color(0xFF94A3B8),
                ),
              ),
              items: months.asMap().entries.map((entry) {
                return DropdownMenuItem<int>(
                  value: entry.key + 1,
                  child: Text(
                    entry.value,
                    style: GoogleFonts.inter(
                      fontSize: 14,
                      color: const Color(0xFF0F172A),
                    ),
                  ),
                );
              }).toList(),
              onChanged: (int? newMonth) {
                onChanged(newMonth, selectedYear);
              },
            ),
          ),
          
          SizedBox(width: 8),
          
          // Year dropdown
          Expanded(
            flex: 2,
            child: DropdownButton<int>(
              value: selectedYear,
              isExpanded: true,
              underline: SizedBox(),
              hint: Text(
                'Godina',
                style: GoogleFonts.inter(
                  fontSize: 14,
                  color: const Color(0xFF94A3B8),
                ),
              ),
              items: List.generate(50, (index) {
                int year = DateTime.now().year - index;
                return DropdownMenuItem<int>(
                  value: year,
                  child: Text(
                    year.toString(),
                    style: GoogleFonts.inter(
                      fontSize: 14,
                      color: const Color(0xFF0F172A),
                    ),
                  ),
                );
              }),
              onChanged: (int? newYear) {
                onChanged(selectedMonth, newYear);
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildWorkExperienceCard(WorkExperience experience, int index) {
    return Container(
      padding: EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border.all(color: const Color(0xFFE2E8F0)),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header sa brojem i delete buttonom
          Row(
            children: [
              Container(
                width: 32,
                height: 32,
                decoration: BoxDecoration(
                  color: const Color(0xFF1E3A8A),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Center(
                  child: Text(
                    '${index + 1}',
                    style: GoogleFonts.inter(
                      fontSize: 14,
                      fontWeight: FontWeight.w700,
                      color: Colors.white,
                    ),
                  ),
                ),
              ),
              SizedBox(width: 12),
              Expanded(
                child: Text(
                  'Radno iskustvo ${index + 1}',
                  style: GoogleFonts.inter(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    color: const Color(0xFF0F172A),
                  ),
                ),
              ),
              IconButton(
                onPressed: () {
                  setState(() {
                    workExperiences[index].dispose();
                    workExperiences.removeAt(index);
                  });
                },
                icon: Icon(
                  Icons.delete_outline,
                  color: const Color(0xFFEF4444),
                ),
              ),
            ],
          ),
          
          SizedBox(height: 20),
          
          // Pozicija
          Text(
            'Pozicija/Radno mjesto',
            style: GoogleFonts.inter(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: const Color(0xFF0F172A),
            ),
          ),
          SizedBox(height: 8),
          TextField(
            controller: experience.positionController,
            onChanged: (value) {
              experience.position = value;
            },
            decoration: InputDecoration(
              hintText: 'Npr. Automehanicar, Elektricar, Vozač...',
              hintStyle: TextStyle(color: const Color(0xFF94A3B8)),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(color: const Color(0xFFE2E8F0)),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(color: const Color(0xFFE2E8F0)),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(color: const Color(0xFF1E3A8A), width: 2),
              ),
              filled: true,
              fillColor: const Color(0xFFFAFBFC),
            ),
          ),
          
          SizedBox(height: 16),
          
          // Kompanija
          Text(
            'Kompanija',
            style: GoogleFonts.inter(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: const Color(0xFF0F172A),
            ),
          ),
          SizedBox(height: 8),
          TextField(
            controller: experience.companyController,
            onChanged: (value) {
              experience.company = value;
            },
            decoration: InputDecoration(
              hintText: 'Unesite naziv kompanije',
              hintStyle: TextStyle(color: const Color(0xFF94A3B8)),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(color: const Color(0xFFE2E8F0)),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(color: const Color(0xFFE2E8F0)),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(color: const Color(0xFF1E3A8A), width: 2),
              ),
              filled: true,
              fillColor: const Color(0xFFFAFBFC),
            ),
          ),
          
          SizedBox(height: 16),
          
          // Mjesto
          Text(
            'Mjesto',
            style: GoogleFonts.inter(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: const Color(0xFF0F172A),
            ),
          ),
          SizedBox(height: 8),
          TextField(
            controller: experience.locationController,
            onChanged: (value) {
              experience.location = value;
            },
            decoration: InputDecoration(
              hintText: 'Unesite mjesto rada',
              hintStyle: TextStyle(color: const Color(0xFF94A3B8)),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(color: const Color(0xFFE2E8F0)),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(color: const Color(0xFFE2E8F0)),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(color: const Color(0xFF1E3A8A), width: 2),
              ),
              filled: true,
              fillColor: const Color(0xFFFAFBFC),
            ),
          ),
          
          SizedBox(height: 20),
          
          // Checkbox za "Trenutno radim ovdje"
          Row(
            children: [
              Checkbox(
                value: experience.isCurrentJob,
                onChanged: (bool? value) {
                  setState(() {
                    experience.isCurrentJob = value ?? false;
                    if (experience.isCurrentJob) {
                      experience.endMonth = null;
                      experience.endYear = null;
                    }
                  });
                },
                activeColor: const Color(0xFF1E3A8A),
              ),
              SizedBox(width: 8),
              Expanded(
                child: Text(
                  'Trenutno radim ovdje',
                  style: GoogleFonts.inter(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: const Color(0xFF0F172A),
                  ),
                ),
              ),
            ],
          ),
          
          SizedBox(height: 16),
          
          // Period rada - Od kada
          Text(
            'Od kada',
            style: GoogleFonts.inter(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              color: const Color(0xFF0F172A),
            ),
          ),
          SizedBox(height: 8),
          _buildMonthYearPicker(
            'Od',
            experience.startMonth,
            experience.startYear,
            (month, year) {
              setState(() {
                experience.startMonth = month;
                experience.startYear = year;
              });
            },
          ),
          
          if (!experience.isCurrentJob) ...[
            SizedBox(height: 16),
            Text(
              'Do kada',
              style: GoogleFonts.inter(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: const Color(0xFF0F172A),
              ),
            ),
            SizedBox(height: 8),
            _buildMonthYearPicker(
              'Do',
              experience.endMonth,
              experience.endYear,
              (month, year) {
                setState(() {
                  experience.endMonth = month;
                  experience.endYear = year;
                });
              },
            ),
          ],
          
          if (experience.isCurrentJob) ...[
            SizedBox(height: 16),
            Container(
              width: double.infinity,
              padding: EdgeInsets.symmetric(vertical: 12, horizontal: 16),
              decoration: BoxDecoration(
                color: const Color(0xFFDCFCE7),
                border: Border.all(color: const Color(0xFF16A34A)),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                'Trenutno zapošljen',
                style: GoogleFonts.inter(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: const Color(0xFF15803D),
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ],
        ],
      ),
    );
  }

  bool _canProceed() {
    switch (currentStep) {
      case 1:
        return selectedCountry != null;
      case 2:
        return locationController.text.trim().isNotEmpty;
      case 3:
        return true; // License selection is optional
      case 4:
        return germanLevel != null && englishLevel != null;
      case 5:
        return educationLevel != null && 
               schoolLocationController.text.trim().isNotEmpty &&
               educationStartMonth != null && 
               educationStartYear != null &&
               educationEndMonth != null &&
               educationEndYear != null;
      case 6:
        if (hasNoWorkExperience) return true;
        return workExperiences.isNotEmpty && 
               workExperiences.every((exp) => 
                 exp.position != null && exp.position!.trim().isNotEmpty &&
                 exp.company != null && exp.company!.trim().isNotEmpty &&
                 exp.location != null && exp.location!.trim().isNotEmpty &&
                 exp.startMonth != null && exp.startYear != null &&
                 (exp.isCurrentJob || (exp.endMonth != null && exp.endYear != null))
               );
      default:
        return false;
    }
  }

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    
    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              const Color(0xFF2665D0),
              const Color(0xFF1E4DB7),
              const Color(0xFF1A3F8C),
            ],
            stops: [0.0, 0.6, 1.0],
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              // Modern AppBar
              Container(
                padding: EdgeInsets.symmetric(horizontal: 20, vertical: 16),
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.1),
                  border: Border(
                    bottom: BorderSide(
                      color: Colors.white.withOpacity(0.2),
                      width: 1,
                    ),
                  ),
                ),
                child: Row(
                  children: [
                    if (widget.isFromProfile) ...[
                      Container(
                        padding: EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: Colors.white.withOpacity(0.15),
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(
                            color: Colors.white.withOpacity(0.2),
                            width: 1,
                          ),
                        ),
                        child: GestureDetector(
                          onTap: () => Navigator.pop(context),
                          child: Icon(
                            Icons.arrow_back_rounded,
                            color: Colors.white,
                            size: 24,
                          ),
                        ),
                      ),
                      SizedBox(width: 16),
                      Expanded(
                        child: Text(
                          'Dodaj radno iskustvo',
                          style: GoogleFonts.outfit(
                            fontSize: 20,
                            fontWeight: FontWeight.w700,
                            color: Colors.white,
                            letterSpacing: -0.5,
                          ),
                        ),
                      ),
                    ] else ...[
                      Expanded(
                        child: Center(
                          child: Container(
                            height: 54,
                            padding: EdgeInsets.all(8),
                            decoration: BoxDecoration(
                              color: Colors.white.withOpacity(0.15),
                              borderRadius: BorderRadius.circular(16),
                              border: Border.all(
                                color: Colors.white.withOpacity(0.2),
                                width: 1,
                              ),
                            ),
                            child: Image.asset(
                              'assets/images/BalkanPosao_logo_small.webp',
                              fit: BoxFit.contain,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ],
                ),
              ),
              // Progress bar
              _buildProgressBar(),
              // Main content
              Expanded(
                child: Container(
                  margin: EdgeInsets.only(top: 8),
                  decoration: BoxDecoration(
                    color: const Color(0xFFFAFBFC),
                    borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(28),
                      topRight: Radius.circular(28),
                    ),
                  ),
                  child: SingleChildScrollView(
                    padding: EdgeInsets.all(24),
                    child: Container(
                      width: double.infinity,
                      padding: EdgeInsets.all(28),
                      decoration: BoxDecoration(
                        color: Colors.white.withOpacity(0.9),
                        borderRadius: BorderRadius.circular(24),
                        border: Border.all(
                          color: Colors.white.withOpacity(0.5),
                          width: 1,
                        ),
                        boxShadow: [
                          BoxShadow(
                            color: const Color(0xFF2665D0).withOpacity(0.1),
                            blurRadius: 20,
                            offset: Offset(0, 8),
                          ),
                        ],
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          if (currentStep == 1) _buildStep1(),
                          if (currentStep == 2) _buildStep2(),
                          if (currentStep == 3) _buildStep3(),
                          if (currentStep == 4) _buildStep4(),
                          if (currentStep == 5) _buildStep5(),
                          if (currentStep == 6) _buildStep6(),
                          SizedBox(height: 40),
                          // Modern Continue Button
                          Container(
                            width: double.infinity,
                            height: 64,
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(20),
                              boxShadow: _canProceed() ? [
                                BoxShadow(
                                  color: const Color(0xFF2665D0).withOpacity(0.3),
                                  blurRadius: 16,
                                  offset: Offset(0, 8),
                                ),
                              ] : [],
                            ),
                            child: ElevatedButton(
                              onPressed: _canProceed() ? _nextStep : null,
                              style: ElevatedButton.styleFrom(
                                backgroundColor: _canProceed() 
                                    ? const Color(0xFF2665D0) 
                                    : const Color(0xFFE2E8F0),
                                foregroundColor: _canProceed() 
                                    ? Colors.white 
                                    : const Color(0xFF64748B),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(20),
                                ),
                                elevation: 0,
                                shadowColor: Colors.transparent,
                              ),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Text(
                                    currentStep == totalSteps ? 'Završi profil' : 'Nastavi',
                                    style: GoogleFonts.outfit(
                                      fontSize: screenWidth * 0.045,
                                      fontWeight: FontWeight.w700,
                                      letterSpacing: 0.5,
                                    ),
                                  ),
                                  if (_canProceed()) ...[
                                    SizedBox(width: 8),
                                    Icon(
                                      currentStep == totalSteps 
                                          ? Icons.check_circle_rounded 
                                          : Icons.arrow_forward_rounded,
                                      size: screenWidth * 0.055,
                                    ),
                                  ],
                                ],
                              ),
                            ),
                          ),
                          SizedBox(height: 20),
                          // Modern Skip Button
                          Center(
                            child: Container(
                              decoration: BoxDecoration(
                                color: const Color(0xFF2665D0).withOpacity(0.1),
                                borderRadius: BorderRadius.circular(16),
                                border: Border.all(
                                  color: const Color(0xFF2665D0).withOpacity(0.2),
                                  width: 1,
                                ),
                              ),
                              child: TextButton(
                                onPressed: _showSkipModal,
                                style: TextButton.styleFrom(
                                  padding: EdgeInsets.symmetric(horizontal: 24, vertical: 16),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(16),
                                  ),
                                ),
                                child: Text(
                                  'Preskoči kreiranje profila',
                                  style: GoogleFonts.inter(
                                    fontSize: 16,
                                    color: const Color(0xFF2665D0),
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  void dispose() {
    locationController.dispose();
    fieldOfStudyController.dispose();
    schoolNameController.dispose();
    schoolLocationController.dispose();
    for (var experience in workExperiences) {
      experience.dispose();
    }
    super.dispose();
  }
}