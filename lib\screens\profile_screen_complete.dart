import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:image_picker/image_picker.dart';
import 'dart:io';
import '../constants/design_constants.dart';

class _StepData {
  final String title;
  final String subtitle;
  final bool completed;
  final IconData icon;
  final List<String> data;

  _StepData({
    required this.title,
    required this.subtitle,
    required this.completed,
    required this.icon,
    required this.data,
  });
}

class ProfileScreenComplete extends StatefulWidget {
  @override
  _ProfileScreenCompleteState createState() => _ProfileScreenCompleteState();
}

class _ProfileScreenCompleteState extends State<ProfileScreenComplete> {
  // Dirty state tracking
  bool _hasChanges = false;
  
  // Profile image
  File? _profileImage;
  final ImagePicker _picker = ImagePicker();
  
  // Mock data - user completed ALL 6 onboarding steps + has profile image
  final Map<String, dynamic> _mockCompleteUserData = {
    'firstName': 'Stefan',
    'lastName': '<PERSON><PERSON><PERSON>',
    'phoneNumber': '+381 62 987 654',
    'country': 'RS',
    'location': 'Novi Sad',
    'drivingLicenses': ['B', 'C', 'CE'],
    'germanLevel': 'b2',
    'englishLevel': 'b1',
    'education': {
      'level': 'srednja',
      'fieldOfStudy': 'Mašinski tehničar',
      'schoolName': 'Tehnička škola "Mihajlo Pupin"',
      'location': 'Novi Sad',
      'startMonth': 9,
      'startYear': 2016,
      'endMonth': 6,
      'endYear': 2020,
    },
    'workExperiences': [
      {
        'position': 'Mašinski radnik',
        'company': 'Delta Holding d.o.o.',
        'location': 'Novi Sad',
        'startMonth': 8,
        'startYear': 2020,
        'endMonth': 3,
        'endYear': 2022,
        'isCurrentJob': false,
      },
      {
        'position': 'CNC operater',
        'company': 'Fiat Chrysler Automobiles',
        'location': 'Kragujevac',
        'startMonth': 4,
        'startYear': 2022,
        'endMonth': null,
        'endYear': null,
        'isCurrentJob': true,
      }
    ],
    'profileImage': true, // Has profile image
  };

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            DesignConstants.backgroundLight,
            DesignConstants.white,
          ],
        ),
      ),
      child: Scaffold(
        backgroundColor: Colors.transparent,
        appBar: AppBar(
          title: Text(
            'Kompletiran profil',
            style: DesignConstants.titleLarge.copyWith(
              color: DesignConstants.black,
            ),
          ),
          backgroundColor: DesignConstants.white.withOpacity(0.95),
          foregroundColor: DesignConstants.modernPrimaryBlue,
          elevation: 0,
          surfaceTintColor: Colors.transparent,
          centerTitle: true,
          iconTheme: IconThemeData(color: DesignConstants.modernPrimaryBlue),
        ),
        body: SingleChildScrollView(
          padding: EdgeInsets.fromLTRB(DesignConstants.spaceL, DesignConstants.spaceM, DesignConstants.spaceL, DesignConstants.spaceL),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
            _buildProfileHeader(),
            SizedBox(height: DesignConstants.spaceXL),
            _buildImageSection(),
            SizedBox(height: DesignConstants.spaceXL),
            _buildProgressAccordion(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildProfileHeader() {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(DesignConstants.spaceXL),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            DesignConstants.white,
            DesignConstants.modernSuccessGreen.withOpacity(0.01),
          ],
        ),
        borderRadius: BorderRadius.circular(DesignConstants.radiusXLarge),
        border: Border.all(
          color: DesignConstants.modernSuccessGreen.withOpacity(0.2),
          width: 1.5,
        ),
        boxShadow: [
          BoxShadow(
            color: DesignConstants.modernSuccessGreen.withOpacity(0.1),
            blurRadius: 12,
            offset: Offset(0, 4),
            spreadRadius: 1,
          ),
          BoxShadow(
            color: Colors.black.withOpacity(0.03),
            blurRadius: 8,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: EdgeInsets.all(DesignConstants.spaceS),
                decoration: BoxDecoration(
                  color: DesignConstants.modernSuccessGreen.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(DesignConstants.radiusMedium),
                ),
                child: Icon(
                  Icons.check_circle_outline,
                  color: DesignConstants.modernSuccessGreen,
                  size: DesignConstants.iconMedium,
                ),
              ),
              SizedBox(width: DesignConstants.spaceM),
              Expanded(
                child: Text(
                  'Vaš profil je kompletiran',
                  style: DesignConstants.titleMedium.copyWith(
                    color: DesignConstants.black,
                  ),
                ),
              ),
              Container(
                padding: EdgeInsets.symmetric(
                  horizontal: DesignConstants.spaceM,
                  vertical: DesignConstants.spaceS,
                ),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      DesignConstants.modernSuccessGreen,
                      DesignConstants.modernSuccessGreenDark,
                    ],
                  ),
                  borderRadius: BorderRadius.circular(DesignConstants.radiusXLarge),
                  boxShadow: [
                    BoxShadow(
                      color: DesignConstants.modernSuccessGreen.withOpacity(0.3),
                      blurRadius: 6,
                      offset: Offset(0, 2),
                    ),
                  ],
                ),
                child: Text(
                  '100%',
                  style: DesignConstants.labelLarge.copyWith(
                    color: DesignConstants.white,
                    fontWeight: FontWeight.w700,
                  ),
                ),
              ),
            ],
          ),
          SizedBox(height: DesignConstants.spaceM),
          Container(
            height: 10,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(DesignConstants.spaceXS),
              color: DesignConstants.lightGray,
            ),
            child: LinearProgressIndicator(
              value: 1.0,
              backgroundColor: Colors.transparent,
              valueColor: AlwaysStoppedAnimation<Color>(DesignConstants.modernSuccessGreen),
              minHeight: 10,
            ),
          ),
          SizedBox(height: DesignConstants.spaceM),
          Row(
            children: [
              Icon(
                Icons.verified_outlined,
                size: 16,
                color: const Color(0xFF10B981),
              ),
              SizedBox(width: DesignConstants.spaceS),
              Expanded(
                child: Text(
                  'Odlično! Vaš profil je sada vidljiv poslodavcima i moći ćete se prijavljivati na sve poslove.',
                  style: DesignConstants.bodyMedium.copyWith(
                    color: DesignConstants.modernSuccessGreen,
                    fontWeight: FontWeight.w500,
                    height: 1.4,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildImageSection() {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(DesignConstants.spaceXL),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            DesignConstants.white,
            DesignConstants.modernPrimaryBlue.withOpacity(0.01),
          ],
        ),
        borderRadius: BorderRadius.circular(DesignConstants.radiusXLarge),
        border: Border.all(
          color: DesignConstants.modernPrimaryBlue.withOpacity(0.1),
          width: 1.5,
        ),
        boxShadow: [
          BoxShadow(
            color: DesignConstants.modernPrimaryBlue.withOpacity(0.05),
            blurRadius: 12,
            offset: Offset(0, 4),
            spreadRadius: 1,
          ),
          BoxShadow(
            color: Colors.black.withOpacity(0.03),
            blurRadius: 8,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Row(
            children: [
              // Profile Image Circle with checkmark overlay
              Stack(
                children: [
                  Container(
                    width: 80,
                    height: 80,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: const Color(0xFF3B82F6),
                      border: Border.all(
                        color: const Color(0xFF10B981),
                        width: 3,
                      ),
                    ),
                    child: ClipOval(
                      child: Center(
                        child: Text(
                          'SJ',
                          style: GoogleFonts.inter(
                            fontSize: 24,
                            fontWeight: FontWeight.w700,
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ),
                  ),
                  Positioned(
                    bottom: 0,
                    right: 0,
                    child: Container(
                      width: 24,
                      height: 24,
                      decoration: BoxDecoration(
                        color: const Color(0xFF10B981),
                        shape: BoxShape.circle,
                        border: Border.all(color: Colors.white, width: 2),
                      ),
                      child: Icon(
                        Icons.check,
                        size: 14,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ],
              ),
              
              SizedBox(width: 16),
              
              // Upload info and button
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Text(
                          'Profilna slika dodana',
                          style: GoogleFonts.inter(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                            color: const Color(0xFF0F172A),
                          ),
                        ),
                        SizedBox(width: 6),
                        Icon(
                          Icons.check_circle,
                          size: 16,
                          color: const Color(0xFF10B981),
                        ),
                      ],
                    ),
                    SizedBox(height: 4),
                    Text(
                      'Perfektno! Poslodavci sada mogu vidjeti vaš profesionalan profil.',
                      style: GoogleFonts.inter(
                        fontSize: 13,
                        color: const Color(0xFF059669),
                        fontWeight: FontWeight.w400,
                      ),
                    ),
                    SizedBox(height: 12),
                    OutlinedButton.icon(
                      onPressed: _pickImage,
                      icon: Icon(
                        Icons.edit,
                        size: 16,
                        color: const Color(0xFF1E3A8A),
                      ),
                      label: Text(
                        'Promijeni sliku',
                        style: GoogleFonts.inter(
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                          color: const Color(0xFF1E3A8A),
                        ),
                      ),
                      style: OutlinedButton.styleFrom(
                        side: BorderSide(color: const Color(0xFF1E3A8A)),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                        padding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildProgressAccordion() {
    final steps = [
      _StepData(
        title: 'Osobni podaci',
        subtitle: 'Ime, prezime, telefon',
        completed: true,
        icon: Icons.person_outline,
        data: [
          'Ime: ${_mockCompleteUserData['firstName']}',
          'Prezime: ${_mockCompleteUserData['lastName']}',
          'Telefon: ${_mockCompleteUserData['phoneNumber']}',
        ]
      ),
      _StepData(
        title: 'Lokacija',
        subtitle: 'Država i mjesto',
        completed: true,
        icon: Icons.location_on_outlined,
        data: [
          'Država: Srbija',
          'Mjesto: ${_mockCompleteUserData['location']}',
        ]
      ),
      _StepData(
        title: 'Vozačka dozvola',
        subtitle: 'Kategorije vozačke dozvole',
        completed: true,
        icon: Icons.drive_eta_outlined,
        data: [
          'Kategorije: ${_mockCompleteUserData['drivingLicenses'].join(', ')}',
        ]
      ),
      _StepData(
        title: 'Jezičko znanje',
        subtitle: 'Njemački i engleski jezik',
        completed: true,
        icon: Icons.language_outlined,
        data: [
          'Njemački: B2 - Više srednje znanje',
          'Engleski: B1 - Srednje znanje',
        ]
      ),
      _StepData(
        title: 'Obrazovanje',
        subtitle: 'Škola i kvalifikacije',
        completed: true,
        icon: Icons.school_outlined,
        data: [
          'Nivo: Srednja škola',
          'Smjer: ${_mockCompleteUserData['education']['fieldOfStudy']}',
          'Škola: ${_mockCompleteUserData['education']['schoolName']}',
          'Mjesto: ${_mockCompleteUserData['education']['location']}',
          'Period: 09/2016 - 06/2020',
        ]
      ),
      _StepData(
        title: 'Radno iskustvo',
        subtitle: '2 pozicije u 2 kompanije',
        completed: true,
        icon: Icons.work_outline,
data: [] // Timeline will be shown instead of simple data list
      ),
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              'Pregled profila',
              style: GoogleFonts.inter(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: const Color(0xFF0F172A),
              ),
            ),
            SizedBox(width: 8),
            Container(
              padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: const Color(0xFF10B981).withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                'Kompletno',
                style: GoogleFonts.inter(
                  fontSize: 12,
                  fontWeight: FontWeight.w600,
                  color: const Color(0xFF10B981),
                ),
              ),
            ),
          ],
        ),
        SizedBox(height: 16),
        
        ...steps.asMap().entries.map((entry) {
          int index = entry.key;
          _StepData step = entry.value;
          
          return Container(
            margin: EdgeInsets.only(bottom: 12),
            child: _buildAccordionItem(
              step.title,
              step.subtitle,
              step.completed,
              step.icon,
              step.data,
              index + 1,
            ),
          );
        }).toList(),
      ],
    );
  }

  Widget _buildAccordionItem(
    String title,
    String subtitle,
    bool completed,
    IconData icon,
    List<String> data,
    int stepNumber,
  ) {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            DesignConstants.white,
            DesignConstants.modernPrimaryBlue.withOpacity(0.01),
          ],
        ),
        borderRadius: BorderRadius.circular(DesignConstants.radiusLarge),
        border: Border.all(
          color: DesignConstants.modernPrimaryBlue.withOpacity(0.1),
          width: 1.5,
        ),
        boxShadow: [
          BoxShadow(
            color: DesignConstants.modernPrimaryBlue.withOpacity(0.05),
            blurRadius: 12,
            offset: Offset(0, 4),
            spreadRadius: 1,
          ),
          BoxShadow(
            color: Colors.black.withOpacity(0.03),
            blurRadius: 8,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: ExpansionTile(
        tilePadding: EdgeInsets.symmetric(horizontal: DesignConstants.spaceL, vertical: DesignConstants.spaceXS),
        childrenPadding: EdgeInsets.fromLTRB(DesignConstants.spaceL, 0, DesignConstants.spaceL, DesignConstants.spaceL),
        leading: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Step number
            Container(
              width: 24,
              height: 24,
              decoration: BoxDecoration(
                color: const Color(0xFF10B981),
                borderRadius: BorderRadius.circular(6),
              ),
              child: Center(
                child: Icon(
                  Icons.check,
                  size: 14,
                  color: Colors.white,
                ),
              ),
            ),
            SizedBox(width: 12),
            // Icon
            Container(
              padding: EdgeInsets.all(6),
              decoration: BoxDecoration(
                color: const Color(0xFF10B981).withOpacity(0.1),
                borderRadius: BorderRadius.circular(6),
              ),
              child: Icon(
                icon,
                size: 16,
                color: const Color(0xFF10B981),
              ),
            ),
          ],
        ),
        title: Text(
          title,
          style: GoogleFonts.inter(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: const Color(0xFF0F172A),
          ),
        ),
        subtitle: Text(
          subtitle,
          style: GoogleFonts.inter(
            fontSize: 13,
            color: const Color(0xFF64748B),
          ),
        ),
        trailing: IconButton(
          onPressed: () => _editSection(title),
          icon: Icon(
            Icons.edit_outlined,
            size: 18,
            color: const Color(0xFF1E3A8A),
          ),
          padding: EdgeInsets.all(4),
          constraints: BoxConstraints(minWidth: 32, minHeight: 32),
        ),
        children: [
          title == 'Radno iskustvo' 
            ? _buildWorkExperienceTimeline()
            : Container(
                width: double.infinity,
                padding: EdgeInsets.all(DesignConstants.spaceM),
                decoration: BoxDecoration(
                  color: DesignConstants.modernSuccessGreen.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(DesignConstants.radiusMedium),
                  border: Border.all(
                    color: DesignConstants.modernSuccessGreen.withOpacity(0.2),
                    width: 1,
                  ),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: data.map((item) {
                    return Padding(
                      padding: EdgeInsets.only(bottom: 6),
                      child: Row(
                        children: [
                          Container(
                            width: 4,
                            height: 4,
                            decoration: BoxDecoration(
                              color: DesignConstants.modernSuccessGreen,
                              shape: BoxShape.circle,
                            ),
                          ),
                          SizedBox(width: DesignConstants.spaceS),
                          Expanded(
                            child: Text(
                              item,
                              style: DesignConstants.bodySmall.copyWith(
                                color: DesignConstants.modernSuccessGreen,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                        ],
                      ),
                    );
                  }).toList(),
                ),
              ),
        ],
      ),
    );
  }


  void _editSection(String sectionTitle) {
    String routeName = '';
    
    switch (sectionTitle) {
      case 'Osobni podaci':
        routeName = '/edit-personal-info';
        break;
      case 'Lokacija':
        routeName = '/edit-location';
        break;
      case 'Vozačka dozvola':
        routeName = '/edit-driving-license';
        break;
      case 'Jezičko znanje':
        routeName = '/edit-languages';
        break;
      case 'Obrazovanje':
        routeName = '/edit-education';
        break;
      case 'Radno iskustvo':
        routeName = '/edit-work-experience';
        break;
    }
    
    if (routeName.isNotEmpty) {
      Navigator.pushNamed(context, routeName);
    }
  }

  Future<void> _pickImage() async {
    try {
      final XFile? image = await _picker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 800,
        maxHeight: 800,
        imageQuality: 85,
      );
      
      if (image != null) {
        setState(() {
          _profileImage = File(image.path);
          _hasChanges = true;
        });
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Greška pri učitavanju slike'),
          backgroundColor: DesignConstants.modernErrorRed,
        ),
      );
    }
  }

  Widget _buildWorkExperienceTimeline() {
    final workExperiences = _mockCompleteUserData['workExperiences'] as List<Map<String, dynamic>>;
    
    // Sort experiences by start date (most recent first)
    workExperiences.sort((a, b) {
      if (a['isCurrentJob'] == true) return -1;
      if (b['isCurrentJob'] == true) return 1;
      
      int yearA = a['startYear'] ?? 0;
      int yearB = b['startYear'] ?? 0;
      return yearB.compareTo(yearA);
    });
    
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(DesignConstants.spaceM),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            DesignConstants.modernSuccessGreen.withOpacity(0.05),
            DesignConstants.modernSuccessGreen.withOpacity(0.02),
          ],
        ),
        borderRadius: BorderRadius.circular(DesignConstants.radiusMedium),
        border: Border.all(
          color: DesignConstants.modernSuccessGreen.withOpacity(0.2),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.timeline_rounded,
                size: DesignConstants.iconMedium,
                color: DesignConstants.modernSuccessGreen,
              ),
              SizedBox(width: DesignConstants.spaceS),
              Text(
                'Radna karijera',
                style: DesignConstants.titleSmall.copyWith(
                  color: DesignConstants.modernSuccessGreen,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          SizedBox(height: DesignConstants.spaceM),
          
          // Timeline items
          ...workExperiences.asMap().entries.map((entry) {
            int index = entry.key;
            Map<String, dynamic> experience = entry.value;
            bool isLast = index == workExperiences.length - 1;
            
            return _buildTimelineItem(
              experience: experience,
              isLast: isLast,
              isFirst: index == 0,
            );
          }).toList(),
        ],
      ),
    );
  }
  
  Widget _buildTimelineItem({
    required Map<String, dynamic> experience,
    required bool isLast,
    required bool isFirst,
  }) {
    bool isCurrentJob = experience['isCurrentJob'] ?? false;
    
    String startDate = '${_getMonthName(experience['startMonth'])} ${experience['startYear']}';
    String endDate = isCurrentJob 
        ? 'Trenutno'
        : '${_getMonthName(experience['endMonth'])} ${experience['endYear']}';
    
    return IntrinsicHeight(
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Timeline line and dot
          Column(
            children: [
              // Timeline dot
              Container(
                width: 16,
                height: 16,
                decoration: BoxDecoration(
                  gradient: isCurrentJob ? LinearGradient(
                    colors: [
                      DesignConstants.modernPrimaryBlue,
                      DesignConstants.modernPrimaryBlueDark,
                    ],
                  ) : null,
                  color: isCurrentJob ? null : DesignConstants.modernSuccessGreen,
                  shape: BoxShape.circle,
                  border: Border.all(
                    color: DesignConstants.white,
                    width: 2,
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: isCurrentJob 
                          ? DesignConstants.modernPrimaryBlue.withOpacity(0.3)
                          : DesignConstants.modernSuccessGreen.withOpacity(0.3),
                      blurRadius: 6,
                      offset: Offset(0, 2),
                    ),
                  ],
                ),
                child: isCurrentJob ? Icon(
                  Icons.play_arrow_rounded,
                  size: 8,
                  color: DesignConstants.white,
                ) : null,
              ),
              // Timeline line (if not last item)
              if (!isLast)
                Container(
                  width: 2,
                  height: 60,
                  margin: EdgeInsets.only(top: 4),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        DesignConstants.modernSuccessGreen.withOpacity(0.8),
                        DesignConstants.modernSuccessGreen.withOpacity(0.3),
                      ],
                    ),
                    borderRadius: BorderRadius.circular(1),
                  ),
                ),
            ],
          ),
          
          SizedBox(width: DesignConstants.spaceM),
          
          // Experience details
          Expanded(
            child: Container(
              margin: EdgeInsets.only(bottom: isLast ? 0 : DesignConstants.spaceL),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Current job indicator
                  if (isCurrentJob)
                    Container(
                      margin: EdgeInsets.only(bottom: DesignConstants.spaceXS),
                      padding: EdgeInsets.symmetric(
                        horizontal: DesignConstants.spaceS,
                        vertical: DesignConstants.spaceXS,
                      ),
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [
                            DesignConstants.modernPrimaryBlue,
                            DesignConstants.modernPrimaryBlueDark,
                          ],
                        ),
                        borderRadius: BorderRadius.circular(DesignConstants.radiusSmall),
                        boxShadow: [
                          BoxShadow(
                            color: DesignConstants.modernPrimaryBlue.withOpacity(0.3),
                            blurRadius: 4,
                            offset: Offset(0, 2),
                          ),
                        ],
                      ),
                      child: Text(
                        'TRENUTNA POZICIJA',
                        style: DesignConstants.captionSmall.copyWith(
                          color: DesignConstants.white,
                          fontWeight: FontWeight.w700,
                          letterSpacing: 0.5,
                        ),
                      ),
                    ),
                  
                  // Job title
                  Text(
                    experience['position'],
                    style: DesignConstants.titleSmall.copyWith(
                      color: DesignConstants.black,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  
                  SizedBox(height: DesignConstants.spaceXS),
                  
                  // Company and location
                  Row(
                    children: [
                      Icon(
                        Icons.business_rounded,
                        size: 14,
                        color: DesignConstants.modernPrimaryBlue,
                      ),
                      SizedBox(width: DesignConstants.spaceXS),
                      Expanded(
                        child: Text(
                          experience['company'],
                          style: DesignConstants.bodySmall.copyWith(
                            color: DesignConstants.modernPrimaryBlue,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ],
                  ),
                  
                  SizedBox(height: DesignConstants.spaceXS),
                  
                  // Location and duration
                  Row(
                    children: [
                      Icon(
                        Icons.location_on_outlined,
                        size: 14,
                        color: DesignConstants.darkGray,
                      ),
                      SizedBox(width: DesignConstants.spaceXS),
                      Text(
                        experience['location'],
                        style: DesignConstants.bodySmall.copyWith(
                          color: DesignConstants.darkGray,
                        ),
                      ),
                      SizedBox(width: DesignConstants.spaceM),
                      Icon(
                        Icons.calendar_today_outlined,
                        size: 14,
                        color: DesignConstants.darkGray,
                      ),
                      SizedBox(width: DesignConstants.spaceXS),
                      Text(
                        '$startDate - $endDate',
                        style: DesignConstants.bodySmall.copyWith(
                          color: DesignConstants.darkGray,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
  
  String _getMonthName(int? month) {
    if (month == null) return '';
    const months = [
      '', 'Jan', 'Feb', 'Mar', 'Apr', 'Maj', 'Jun',
      'Jul', 'Avg', 'Sep', 'Okt', 'Nov', 'Dec'
    ];
    return month >= 1 && month <= 12 ? months[month] : '';
  }

}