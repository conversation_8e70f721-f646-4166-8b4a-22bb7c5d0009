import 'package:flutter/material.dart';
import '../constants/design_constants.dart';

class AddWorkExperienceScreen extends StatefulWidget {
  final Map<String, dynamic>? existingExperience;
  final int? editIndex;

  const AddWorkExperienceScreen({
    Key? key,
    this.existingExperience,
    this.editIndex,
  }) : super(key: key);

  @override
  _AddWorkExperienceScreenState createState() => _AddWorkExperienceScreenState();
}

class _AddWorkExperienceScreenState extends State<AddWorkExperienceScreen> {
  final _formKey = GlobalKey<FormState>();
  bool _hasChanges = false;
  
  // Controllers
  late TextEditingController _positionController;
  late TextEditingController _companyController;
  late TextEditingController _locationController;
  
  // Dropdowns
  int? _startMonth;
  int? _startYear;
  int? _endMonth;
  int? _endYear;
  bool _isCurrentJob = false;
  
  final List<String> _months = [
    'Januar', 'Februar', 'Mart', 'April', '<PERSON>', 'Juni',
    'Juli', 'August', 'Septembar', 'Oktobar', 'Novembar', 'Decembar'
  ];
  
  @override
  void initState() {
    super.initState();
    
    // Initialize with existing data if editing
    if (widget.existingExperience != null) {
      final exp = widget.existingExperience!;
      _positionController = TextEditingController(text: exp['position'] ?? '');
      _companyController = TextEditingController(text: exp['company'] ?? '');
      _locationController = TextEditingController(text: exp['location'] ?? '');
      _startMonth = exp['startMonth'];
      _startYear = exp['startYear'];
      _endMonth = exp['endMonth'];
      _endYear = exp['endYear'];
      _isCurrentJob = exp['isCurrentJob'] ?? false;
    } else {
      _positionController = TextEditingController();
      _companyController = TextEditingController();
      _locationController = TextEditingController();
    }
    
    _positionController.addListener(_onFieldChanged);
    _companyController.addListener(_onFieldChanged);
    _locationController.addListener(_onFieldChanged);
  }
  
  @override
  void dispose() {
    _positionController.dispose();
    _companyController.dispose();
    _locationController.dispose();
    super.dispose();
  }
  
  void _onFieldChanged() {
    if (!_hasChanges) {
      setState(() {
        _hasChanges = true;
      });
    }
  }
  
  @override
  Widget build(BuildContext context) {
    final isEditing = widget.existingExperience != null;
    
    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: DesignConstants.modernPrimaryGradient,
        ),
        child: SafeArea(
          child: Stack(
            children: [
              Column(
                children: [
                  _buildModernAppBar(isEditing),
                  Expanded(
                    child: Container(
                      margin: EdgeInsets.only(top: 20),
                      decoration: BoxDecoration(
                        color: DesignConstants.backgroundGray,
                        borderRadius: BorderRadius.vertical(
                          top: Radius.circular(30),
                        ),
                      ),
                      child: SingleChildScrollView(
                        padding: EdgeInsets.fromLTRB(20, 30, 20, _hasChanges ? 100 : 20),
                        child: Form(
                          key: _formKey,
                          child: Column(
                            children: [
                              _buildModernHeader(isEditing),
                              SizedBox(height: 24),
                              _buildModernFormCard(),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
              if (_hasChanges) _buildFloatingSaveButton(isEditing),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildModernAppBar(bool isEditing) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 20, vertical: 15),
      child: Row(
        children: [
          Container(
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.2),
              borderRadius: BorderRadius.circular(12),
            ),
            child: IconButton(
              onPressed: () => Navigator.pop(context),
              icon: Icon(
                Icons.arrow_back_ios_new,
                color: Colors.white,
                size: 20,
              ),
            ),
          ),
          Expanded(
            child: Center(
              child: Text(
                isEditing ? 'Uredi iskustvo' : 'Novo iskustvo',
                style: DesignConstants.titleMedium.copyWith(
                  color: Colors.white,
                ),
              ),
            ),
          ),
          SizedBox(width: 48),
        ],
      ),
    );
  }

  Widget _buildModernHeader(bool isEditing) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: DesignConstants.shadowMedium,
        border: Border.all(
          color: DesignConstants.borderGray,
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: EdgeInsets.all(16),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  DesignConstants.modernPrimaryBlue.withOpacity(0.1),
                  DesignConstants.modernPrimaryBlue.withOpacity(0.05),
                ],
              ),
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: DesignConstants.modernPrimaryBlue.withOpacity(0.2),
                width: 1,
              ),
            ),
            child: Icon(
              isEditing ? Icons.edit_outlined : Icons.add_circle_outline,
              color: DesignConstants.modernPrimaryBlue,
              size: 28,
            ),
          ),
          SizedBox(width: 20),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  isEditing ? 'Uredi radno iskustvo' : 'Dodaj novo radno iskustvo',
                  style: DesignConstants.titleSmall.copyWith(
                    color: DesignConstants.black,
                  ),
                ),
                SizedBox(height: 8),
                Text(
                  isEditing 
                    ? 'Uredite detalje vašeg radnog iskustva' 
                    : 'Dodajte informacije o vašoj radnoj poziciji',
                  style: DesignConstants.bodyMedium.copyWith(
                    color: DesignConstants.darkGray,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildModernFormCard() {
    return Container(
      padding: EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: DesignConstants.shadowMedium,
        border: Border.all(
          color: DesignConstants.borderGray,
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildModernTextField(
            label: 'Pozicija / Radno mjesto',
            controller: _positionController,
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Molimo unesite poziciju';
              }
              return null;
            },
          ),
          SizedBox(height: 24),
          _buildModernTextField(
            label: 'Kompanija',
            controller: _companyController,
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Molimo unesite naziv kompanije';
              }
              return null;
            },
          ),
          SizedBox(height: 24),
          _buildModernTextField(
            label: 'Lokacija',
            controller: _locationController,
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Molimo unesite lokaciju';
              }
              return null;
            },
          ),
          SizedBox(height: 24),
          _buildModernCurrentJobCheckbox(),
          SizedBox(height: 24),
          _buildModernDateRange(),
        ],
      ),
    );
  }

  Widget _buildModernTextField({
    required String label,
    required TextEditingController controller,
    String? Function(String?)? validator,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: DesignConstants.labelMedium.copyWith(
            color: DesignConstants.black,
          ),
        ),
        SizedBox(height: 8),
        Container(
          decoration: BoxDecoration(
            color: DesignConstants.surfaceGray,
            borderRadius: BorderRadius.circular(16),
            border: Border.all(
              color: DesignConstants.lightGray,
              width: 1,
            ),
          ),
          child: TextFormField(
            controller: controller,
            validator: validator,
            style: DesignConstants.bodyLarge.copyWith(
              color: DesignConstants.black,
            ),
            decoration: InputDecoration(
              border: InputBorder.none,
              contentPadding: EdgeInsets.symmetric(horizontal: 20, vertical: 16),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildModernCurrentJobCheckbox() {
    return Container(
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: DesignConstants.surfaceGray,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: DesignConstants.lightGray,
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Container(
            width: 24,
            height: 24,
            decoration: BoxDecoration(
              color: _isCurrentJob ? DesignConstants.modernPrimaryBlue : Colors.transparent,
              borderRadius: BorderRadius.circular(6),
              border: Border.all(
                color: _isCurrentJob ? DesignConstants.modernPrimaryBlue : DesignConstants.lightGray,
                width: 2,
              ),
            ),
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                borderRadius: BorderRadius.circular(6),
                onTap: () {
                  setState(() {
                    _isCurrentJob = !_isCurrentJob;
                    if (_isCurrentJob) {
                      _endMonth = null;
                      _endYear = null;
                    }
                    _hasChanges = true;
                  });
                },
                child: _isCurrentJob
                  ? Icon(
                      Icons.check,
                      size: 16,
                      color: Colors.white,
                    )
                  : null,
              ),
            ),
          ),
          SizedBox(width: 16),
          Expanded(
            child: Text(
              'Trenutno radim na ovoj poziciji',
              style: DesignConstants.bodyMedium.copyWith(
                color: DesignConstants.black,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildModernDateRange() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Period rada',
          style: DesignConstants.labelMedium.copyWith(
            color: DesignConstants.black,
          ),
        ),
        SizedBox(height: 16),
        _buildModernDateDropdown('Početak rada', _startMonth, _startYear, true),
        if (!_isCurrentJob) ...[
          SizedBox(height: 20),
          _buildModernDateDropdown('Završetak rada', _endMonth, _endYear, false),
        ],
      ],
    );
  }

  Widget _buildModernDateDropdown(String label, int? month, int? year, bool isStart) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: DesignConstants.captionMedium.copyWith(
            color: DesignConstants.darkGray,
          ),
        ),
        SizedBox(height: 8),
        Row(
          children: [
            Expanded(
              flex: 3,
              child: Container(
                decoration: BoxDecoration(
                  color: DesignConstants.surfaceGray,
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(
                    color: DesignConstants.lightGray,
                    width: 1,
                  ),
                ),
                child: DropdownButtonFormField<int>(
                  value: month,
                  onChanged: (int? value) {
                    setState(() {
                      if (isStart) {
                        _startMonth = value;
                      } else {
                        _endMonth = value;
                      }
                      _hasChanges = true;
                    });
                  },
                  decoration: InputDecoration(
                    border: InputBorder.none,
                    contentPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                  ),
                  style: DesignConstants.bodySmall.copyWith(
                    color: DesignConstants.black,
                  ),
                  items: List.generate(12, (index) {
                    return DropdownMenuItem<int>(
                      value: index + 1,
                      child: Text(
                        _months[index],
                        overflow: TextOverflow.ellipsis,
                      ),
                    );
                  }),
                  validator: (value) {
                    if (value == null) {
                      return 'Obavezno';
                    }
                    return null;
                  },
                ),
              ),
            ),
            SizedBox(width: 12),
            Expanded(
              flex: 2,
              child: Container(
                decoration: BoxDecoration(
                  color: DesignConstants.surfaceGray,
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(
                    color: DesignConstants.lightGray,
                    width: 1,
                  ),
                ),
                child: DropdownButtonFormField<int>(
                  value: year,
                  onChanged: (int? value) {
                    setState(() {
                      if (isStart) {
                        _startYear = value;
                      } else {
                        _endYear = value;
                      }
                      _hasChanges = true;
                    });
                  },
                  decoration: InputDecoration(
                    border: InputBorder.none,
                    contentPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                  ),
                  style: DesignConstants.bodySmall.copyWith(
                    color: DesignConstants.black,
                  ),
                  items: List.generate(50, (index) {
                    int year = DateTime.now().year - index;
                    return DropdownMenuItem<int>(
                      value: year,
                      child: Text(year.toString()),
                    );
                  }),
                  validator: (value) {
                    if (value == null) {
                      return 'Obavezno';
                    }
                    return null;
                  },
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildFloatingSaveButton(bool isEditing) {
    return Positioned(
      bottom: 30,
      left: 20,
      right: 20,
      child: Container(
        decoration: BoxDecoration(
          boxShadow: [
            BoxShadow(
              color: DesignConstants.modernPrimaryBlue.withOpacity(0.3),
              blurRadius: 20,
              offset: Offset(0, 8),
            ),
          ],
        ),
        child: ElevatedButton(
          onPressed: _saveExperience,
          style: ElevatedButton.styleFrom(
            backgroundColor: DesignConstants.modernPrimaryBlue,
            foregroundColor: Colors.white,
            elevation: 0,
            shadowColor: Colors.transparent,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
            ),
            padding: EdgeInsets.symmetric(vertical: 18),
            minimumSize: Size(double.infinity, 56),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(isEditing ? Icons.save_outlined : Icons.add_circle_outline, size: 22),
              SizedBox(width: 12),
              Text(
                isEditing ? 'Spremi promjene' : 'Dodaj radno iskustvo',
                style: DesignConstants.buttonLarge,
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _saveExperience() {
    if (_formKey.currentState!.validate()) {
      final experienceData = {
        'position': _positionController.text,
        'company': _companyController.text,
        'location': _locationController.text,
        'startMonth': _startMonth,
        'startYear': _startYear,
        'endMonth': _isCurrentJob ? null : _endMonth,
        'endYear': _isCurrentJob ? null : _endYear,
        'isCurrentJob': _isCurrentJob,
      };
      
      // Return the experience data back to the calling screen
      Navigator.pop(context, {
        'experienceData': experienceData,
        'editIndex': widget.editIndex,
      });
    }
  }
}
