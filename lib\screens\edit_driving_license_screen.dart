import 'package:flutter/material.dart';
import '../constants/design_constants.dart';

class EditDrivingLicenseScreen extends StatefulWidget {
  @override
  _EditDrivingLicenseScreenState createState() => _EditDrivingLicenseScreenState();
}

class _EditDrivingLicenseScreenState extends State<EditDrivingLicenseScreen> {
  bool _hasChanges = false;
  
  // Available driving license categories
  final Map<String, String> _licenseCategories = {
    'A': 'A - Motocikli',
    'A1': 'A1 - Laki motocikli',
    'A2': 'A2 - Srednji motocikli',
    'B': 'B - Osobni automobili',
    'BE': 'BE - Osobni automobili s prikolicom',
    'C': 'C - Teretni automobili',
    'CE': 'CE - Teretni automobili s prikolicom',
    'C1': 'C1 - Mali teretni automobili',
    'C1E': 'C1E - Mali teretni s prikolicom',
    'D': 'D - Autobusi',
    'DE': 'DE - Autobusi s prikolicom',
    'D1': 'D1 - Mali autobusi',
    'D1E': 'D1E - Mali autobusi s prikolicom',
  };
  
  // Selected licenses - initialize with current data
  Set<String> _selectedLicenses = {'B', 'C', 'CE'};
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.center,
            colors: [
              DesignConstants.modernPrimaryBlue,
              DesignConstants.modernPrimaryBlue.withOpacity(0.1),
            ],
            stops: [0.0, 0.3],
          ),
        ),
        child: SafeArea(
          child: Stack(
            children: [
              Column(
                children: [
                  // Custom AppBar with gradient
                  Container(
                    padding: EdgeInsets.symmetric(horizontal: 20, vertical: 16),
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.15),
                      border: Border(
                        bottom: BorderSide(
                          color: Colors.white.withOpacity(0.2),
                          width: 1,
                        ),
                      ),
                    ),
                    child: Row(
                      children: [
                        Container(
                          decoration: BoxDecoration(
                            color: Colors.white.withOpacity(0.2),
                            borderRadius: BorderRadius.circular(DesignConstants.radiusMedium),
                            border: Border.all(
                              color: Colors.white.withOpacity(0.3),
                              width: 1,
                            ),
                          ),
                          child: IconButton(
                            onPressed: () => Navigator.pop(context),
                            icon: Icon(
                              Icons.arrow_back_ios,
                              color: DesignConstants.white,
                              size: DesignConstants.iconMedium,
                            ),
                            padding: EdgeInsets.all(8),
                          ),
                        ),
                        Expanded(
                          child: Text(
                            'Uredi vozačku dozvolu',
                            textAlign: TextAlign.center,
                            style: DesignConstants.titleMedium.copyWith(
                              color: DesignConstants.white,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                        // Invisible container for balance
                        Container(width: 48, height: 48),
                      ],
                    ),
                  ),
                  // Content area
                  Expanded(
                    child: Container(
                      width: double.infinity,
                      decoration: BoxDecoration(
                        color: DesignConstants.surfaceGray,
                        borderRadius: BorderRadius.only(
                          topLeft: Radius.circular(DesignConstants.radiusXLarge),
                          topRight: Radius.circular(DesignConstants.radiusXLarge),
                        ),
                      ),
                      child: SingleChildScrollView(
                        padding: EdgeInsets.fromLTRB(20, 20, 20, 100), // Bottom padding for floating button
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            _buildModernHeader(),
                            SizedBox(height: DesignConstants.spaceXXL),
                            _buildModernLicenseSelection(),
                          ],
                        ),
                      ),
                    ),
                  ),
                ],
              ),
              
              // Floating Save Button
              if (_hasChanges)
                Positioned(
                  left: 20,
                  right: 20,
                  bottom: 20,
                  child: _buildFloatingSaveButton(),
                ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildModernHeader() {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(DesignConstants.spaceXXL),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            DesignConstants.white,
            DesignConstants.modernPrimaryBlue.withOpacity(0.02),
          ],
        ),
        borderRadius: BorderRadius.circular(DesignConstants.radiusXXLarge),
        border: Border.all(
          color: DesignConstants.modernPrimaryBlue.withOpacity(0.15),
          width: 1.5,
        ),
        boxShadow: [
          BoxShadow(
            color: DesignConstants.modernPrimaryBlue.withOpacity(0.08),
            blurRadius: 20,
            offset: Offset(0, 8),
            spreadRadius: 1,
          ),
          BoxShadow(
            color: Colors.black.withOpacity(0.03),
            blurRadius: 8,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: EdgeInsets.all(DesignConstants.spaceM),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      DesignConstants.modernPrimaryBlue.withOpacity(0.1),
                      DesignConstants.modernPrimaryBlue.withOpacity(0.05),
                    ],
                  ),
                  borderRadius: BorderRadius.circular(DesignConstants.radiusLarge),
                ),
                child: Icon(
                  Icons.drive_eta_outlined,
                  color: DesignConstants.modernPrimaryBlue,
                  size: DesignConstants.iconLarge,
                ),
              ),
              SizedBox(width: DesignConstants.spaceL),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Vozačka dozvola',
                      style: DesignConstants.titleMedium.copyWith(
                        color: DesignConstants.black,
                      ),
                    ),
                    SizedBox(height: DesignConstants.spaceXS),
                    Text(
                      'Uredite kategorije vozačkih dozvola koje posedujete',
                      style: DesignConstants.bodyMedium.copyWith(
                        color: DesignConstants.darkGray,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildModernLicenseSelection() {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(DesignConstants.spaceXXL),
      decoration: BoxDecoration(
        color: DesignConstants.white,
        borderRadius: BorderRadius.circular(DesignConstants.radiusXLarge),
        border: Border.all(
          color: DesignConstants.lightGray.withOpacity(0.5),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.04),
            blurRadius: 16,
            offset: Offset(0, 4),
          ),
          BoxShadow(
            color: Colors.black.withOpacity(0.02),
            blurRadius: 4,
            offset: Offset(0, 1),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: EdgeInsets.all(6),
                decoration: BoxDecoration(
                  color: DesignConstants.modernPrimaryBlue.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(6),
                ),
                child: Icon(
                  Icons.assignment_outlined,
                  size: 16,
                  color: DesignConstants.modernPrimaryBlue,
                ),
              ),
              SizedBox(width: DesignConstants.spaceS),
              Text(
                'Kategorije vozačke dozvole',
                style: DesignConstants.labelMedium.copyWith(
                  color: DesignConstants.black,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          SizedBox(height: DesignConstants.spaceL),
          Text(
            'Odaberite sve kategorije koje posjedujete:',
            style: DesignConstants.bodyMedium.copyWith(
              color: DesignConstants.darkGray,
            ),
          ),
          SizedBox(height: DesignConstants.spaceXL),
          
          // Personal vehicle categories
          _buildModernCategoryGroup(
            'Osobna vozila',
            ['A', 'A1', 'A2', 'B', 'BE'],
            Icons.directions_car_outlined,
          ),
          
          SizedBox(height: DesignConstants.spaceXXL),
          
          // Commercial vehicle categories
          _buildModernCategoryGroup(
            'Komercijalna vozila',
            ['C', 'CE', 'C1', 'C1E', 'D', 'DE', 'D1', 'D1E'],
            Icons.local_shipping_outlined,
          ),
        ],
      ),
    );
  }

  Widget _buildModernCategoryGroup(String title, List<String> categories, IconData icon) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Container(
              padding: EdgeInsets.all(4),
              decoration: BoxDecoration(
                color: DesignConstants.darkGray.withOpacity(0.1),
                borderRadius: BorderRadius.circular(4),
              ),
              child: Icon(
                icon,
                size: 16,
                color: DesignConstants.darkGray,
              ),
            ),
            SizedBox(width: DesignConstants.spaceS),
            Text(
              title,
              style: DesignConstants.labelMedium.copyWith(
                color: DesignConstants.black,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
        SizedBox(height: DesignConstants.spaceM),
        Wrap(
          spacing: DesignConstants.spaceS,
          runSpacing: DesignConstants.spaceS,
          children: categories.map((category) => _buildModernLicenseChip(category)).toList(),
        ),
      ],
    );
  }

  Widget _buildModernLicenseChip(String category) {
    final bool isSelected = _selectedLicenses.contains(category);
    
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(DesignConstants.radiusMedium),
        boxShadow: isSelected ? [
          BoxShadow(
            color: DesignConstants.modernPrimaryBlue.withOpacity(0.2),
            blurRadius: 8,
            offset: Offset(0, 2),
          ),
        ] : [],
      ),
      child: FilterChip(
        label: Text(
          _licenseCategories[category] ?? category,
          style: DesignConstants.labelSmall.copyWith(
            color: isSelected ? DesignConstants.white : DesignConstants.black,
            fontWeight: FontWeight.w600,
          ),
        ),
        selected: isSelected,
        onSelected: (bool selected) {
          setState(() {
            if (selected) {
              _selectedLicenses.add(category);
            } else {
              _selectedLicenses.remove(category);
            }
            _hasChanges = true;
          });
        },
        selectedColor: DesignConstants.modernPrimaryBlue,
        checkmarkColor: DesignConstants.white,
        backgroundColor: DesignConstants.surfaceGray,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(DesignConstants.radiusMedium),
          side: BorderSide(
            color: isSelected 
                ? DesignConstants.modernPrimaryBlue 
                : DesignConstants.lightGray.withOpacity(0.5),
            width: isSelected ? 2 : 1,
          ),
        ),
        padding: EdgeInsets.symmetric(
          horizontal: DesignConstants.spaceM, 
          vertical: DesignConstants.spaceS,
        ),
      ),
    );
  }

  Widget _buildFloatingSaveButton() {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        gradient: DesignConstants.modernPrimaryGradient,
        borderRadius: BorderRadius.circular(DesignConstants.radiusLarge),
        boxShadow: [
          BoxShadow(
            color: DesignConstants.modernPrimaryBlue.withOpacity(0.4),
            blurRadius: 20,
            offset: Offset(0, 8),
          ),
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: _saveChanges,
          borderRadius: BorderRadius.circular(DesignConstants.radiusLarge),
          child: Container(
            padding: EdgeInsets.symmetric(
              vertical: DesignConstants.spaceL + 2,
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.check_circle_outline,
                  color: DesignConstants.white,
                  size: DesignConstants.iconMedium,
                ),
                SizedBox(width: DesignConstants.spaceS),
                Text(
                  'Spremi promjene',
                  style: DesignConstants.buttonLarge.copyWith(
                    color: DesignConstants.white,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _saveChanges() {
    // TODO: Save changes to backend/state management
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Container(
          padding: EdgeInsets.symmetric(vertical: 4),
          child: Row(
            children: [
              Container(
                padding: EdgeInsets.all(6),
                decoration: BoxDecoration(
                  color: DesignConstants.white.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Icon(
                  Icons.check_circle,
                  color: DesignConstants.white,
                  size: 16,
                ),
              ),
              SizedBox(width: DesignConstants.spaceM),
              Expanded(
                child: Text(
                  'Vozačka dozvola uspješno ažurirana!',
                  style: DesignConstants.labelMedium.copyWith(
                    color: DesignConstants.white,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
        ),
        backgroundColor: DesignConstants.modernSuccessGreen,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(DesignConstants.radiusMedium),
        ),
        margin: EdgeInsets.all(DesignConstants.spaceL),
        elevation: 8,
        duration: Duration(seconds: 3),
      ),
    );
    
    Navigator.pop(context, true);
  }
}
