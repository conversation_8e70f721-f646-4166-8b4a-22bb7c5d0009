# IMPLEMENTATION TODO - Code Comments Guide

## 🚨 PRIORITET 1 - KRITIČNO

### **Authentication & User Management**

#### `lib/screens/register_screen.dart`
```dart
// TODO: IMPLEMENTIRATI - LINE 245
// MOCK DATA - zamijeniti sa pravim API call-om
Future<void> _registerUser() async {
  // → IMPLEMENTIRATI: POST /api/auth/register
  // → Poslati: firstName, lastName, phone, countryCode
  // → Koristiti: http package ili dio package
  Navigator.pushReplacement(
    context,
    MaterialPageRoute(builder: (context) => OnboardingScreen(...))
  );
}
```

#### `lib/screens/login_screen.dart`  
```dart
// TODO: IMPLEMENTIRATI - LINE 89
// MOCK DATA - trenutno automatski login
void _signIn() async {
  // → IMPLEMENTIRATI: POST /api/auth/login
  // → Poslati: phone/email, password
  // → Spremiti: access_token u SharedPreferences
  Navigator.pushReplacement(
    context,
    MaterialPageRoute(builder: (context) => MainScreen()),
  );
}
```

#### `lib/screens/onboarding_screen.dart`
```dart
// TODO: IMPLEMENTIRATI - LINES 134-152  
void _nextStep() {
  if (currentStep < totalSteps) {
    // Standard step progression
  } else {
    // → IMPLEMENTIRATI: POST /api/users/{id}/profile
    // → Poslati sve onboarding podatke:
    /*
    {
      "country": selectedCountry,
      "location": locationController.text,
      "drivingLicenses": selectedLicenses.toList(),
      "germanLevel": germanLevel,
      "englishLevel": englishLevel,
      "education": {
        "level": educationLevel,
        "fieldOfStudy": fieldOfStudyController.text,
        "schoolLocation": schoolLocationController.text,
        "startMonth": educationStartMonth,
        "startYear": educationStartYear,
        "endMonth": educationEndMonth,
        "endYear": educationEndYear
      },
      "workExperiences": workExperiences.map((exp) => {
        "position": exp.position,
        "company": exp.company,
        "location": exp.location,
        "startMonth": exp.startMonth,
        "startYear": exp.startYear,
        "endMonth": exp.endMonth,
        "endYear": exp.endYear,
        "isCurrentJob": exp.isCurrentJob
      }).toList()
    }
    */
    
    Navigator.pushReplacement(
      context,
      MaterialPageRoute(builder: (context) => MainScreen()),
    );
  }
}
```

---

## 🚨 PRIORITET 1 - JOB LISTINGS

#### `lib/screens/jobs_screen.dart`
```dart
// TODO: IMPLEMENTIRATI - LINES 25-50
class _JobsScreenState extends State<JobsScreen> {
  // MOCK DATA - zamijeniti sa API calls
  
  @override
  void initState() {
    super.initState();
    // → IMPLEMENTIRATI: loadFeaturedJobs() i loadRegularJobs()
    _loadJobs();
  }
  
  Future<void> _loadJobs() async {
    // → IMPLEMENTIRATI: GET /api/jobs?featured=true
    // final featuredResponse = await api.get('/api/jobs?featured=true');
    // setState(() { featuredJobs = featuredResponse.data; });
    
    // → IMPLEMENTIRATI: GET /api/jobs?page=1&limit=20
    // final jobsResponse = await api.get('/api/jobs?page=1&limit=20');  
    // setState(() { regularJobs = jobsResponse.data; });
  }
  
  // TODO: IMPLEMENTIRATI - LINES 200-220
  void _applyFilters() {
    // → IMPLEMENTIRATI: GET /api/jobs?location={}&workType={}&salaryRange={}
    // Rebuild job list based on filters
  }
}
```

#### `lib/screens/job_details_screen.dart`
```dart
// TODO: IMPLEMENTIRATI - LINES 15-40
class JobDetailsScreen extends StatefulWidget {
  // → DODATI: final String jobId; u constructor
  
  @override
  void initState() {
    super.initState();
    _loadJobDetails();
  }
  
  Future<void> _loadJobDetails() async {
    // → IMPLEMENTIRATI: GET /api/jobs/{jobId}
    // final jobDetails = await api.get('/api/jobs/${widget.jobId}');
    // setState(() { job = jobDetails.data; });
  }
  
  // TODO: IMPLEMENTIRATI - LINE 280  
  void _applyToJob() async {
    // → IMPLEMENTIRATI: POST /api/jobs/{jobId}/apply
    // final applicationResult = await api.post('/api/jobs/${widget.jobId}/apply');
    // if (applicationResult.success) {
    //   Show success message
    //   Navigate back or to applications
    // }
  }
}
```

---

## 🚨 PRIORITET 1 - USER APPLICATIONS

#### `lib/screens/applications_screen.dart`
```dart
// TODO: IMPLEMENTIRATI - LINES 20-40
class _ApplicationsScreenState extends State<ApplicationsScreen> {
  List<Map<String, dynamic>> applications = [];
  bool isLoading = true;
  
  @override
  void initState() {
    super.initState();
    _loadApplications();
  }
  
  Future<void> _loadApplications() async {
    // → IMPLEMENTIRATI: GET /api/users/{userId}/applications
    // final response = await api.get('/api/users/$userId/applications');
    // setState(() {
    //   applications = response.data;
    //   isLoading = false;
    // });
  }
}
```

#### `lib/screens/application_details_screen.dart`
```dart
// TODO: IMPLEMENTIRATI - LINES 25-45
void _loadApplicationDetails() async {
  // → IMPLEMENTIRATI: GET /api/applications/{applicationId}
  // final applicationDetails = await api.get('/api/applications/${widget.applicationId}');
  // setState(() { application = applicationDetails.data; });
}

// TODO: IMPLEMENTIRATI - LINE 180
void _withdrawApplication() async {
  // → IMPLEMENTIRATI: DELETE /api/applications/{applicationId}
  // final result = await api.delete('/api/applications/${widget.applicationId}');
  // if (result.success) {
  //   Navigator.pop(context);
  //   Show success message
  // }
}
```

---

## 🚨 PRIORITET 2 - CHAT & MESSAGING

#### `lib/screens/messages_screen.dart`
```dart
// TODO: IMPLEMENTIRATI - LINES 25-50
class _MessagesScreenState extends State<MessagesScreen> {
  List<Map<String, dynamic>> conversations = [];
  
  @override
  void initState() {
    super.initState();
    _loadConversations();
    // → IMPLEMENTIRATI: WebSocket connection za real-time updates
    _setupRealtimeUpdates();
  }
  
  Future<void> _loadConversations() async {
    // → IMPLEMENTIRATI: GET /api/users/{userId}/conversations
    // final response = await api.get('/api/users/$userId/conversations');
    // setState(() { conversations = response.data; });
  }
  
  void _setupRealtimeUpdates() {
    // → IMPLEMENTIRATI: WebSocket ili Socket.io
    // socket.on('new_message', (data) => {
    //   Update conversation list
    //   Update unread counts
    // });
  }
}
```

#### `lib/screens/chat_screen.dart`
```dart
// TODO: IMPLEMENTIRATI - LINES 20-60
class _ChatScreenState extends State<ChatScreen> {
  List<Map<String, dynamic>> messages = [];
  final TextEditingController _messageController = TextEditingController();
  
  @override
  void initState() {
    super.initState();
    _loadMessages();
    _setupRealtimeChat();
  }
  
  Future<void> _loadMessages() async {
    // → IMPLEMENTIRATI: GET /api/conversations/{conversationId}/messages
    // final response = await api.get('/api/conversations/${widget.conversationId}/messages');
    // setState(() { messages = response.data; });
  }
  
  void _setupRealtimeChat() {
    // → IMPLEMENTIRATI: WebSocket connection
    // socket.on('new_message_${widget.conversationId}', (message) => {
    //   setState(() { messages.add(message); });
    // });
  }
  
  // TODO: IMPLEMENTIRATI - LINE 180
  void _sendMessage() async {
    String messageText = _messageController.text.trim();
    if (messageText.isEmpty) return;
    
    // → IMPLEMENTIRATI: POST /api/conversations/{conversationId}/messages
    // final response = await api.post('/api/conversations/${widget.conversationId}/messages', {
    //   'text': messageText,
    //   'type': 'text'
    // });
    
    _messageController.clear();
  }
}
```

---

## 🚨 PRIORITET 2 - PROFILE MANAGEMENT

#### `lib/screens/profile_screen_new.dart`
```dart
// TODO: IMPLEMENTIRATI - LINES 38-90
class _ProfileScreenNewState extends State<ProfileScreenNew> {
  // MOCK USER DATA - zamijeniti sa API call
  Map<String, dynamic> _userData = {};
  bool _isLoading = true;
  
  @override
  void initState() {
    super.initState();
    _loadUserProfile();
  }
  
  Future<void> _loadUserProfile() async {
    // → IMPLEMENTIRATI: GET /api/users/{userId}/profile
    // final response = await api.get('/api/users/$userId/profile');
    // setState(() {
    //   _userData = response.data;
    //   _isLoading = false;
    // });
  }
  
  // TODO: IMPLEMENTIRATI - LINE 250
  void _saveProfile() async {
    if (!_hasChanges) return;
    
    // → IMPLEMENTIRATI: PUT /api/users/{userId}/profile
    // final response = await api.put('/api/users/$userId/profile', _userData);
    // if (response.success) {
    //   setState(() { _hasChanges = false; });
    //   Show success message
    // }
  }
  
  // TODO: IMPLEMENTIRATI - LINE 180
  void _uploadProfileImage() async {
    // → IMPLEMENTIRATI: POST /api/users/{userId}/profile-image
    // final response = await api.uploadFile('/api/users/$userId/profile-image', imageFile);
    // if (response.success) {
    //   Update profile image URL
    // }
  }
}
```

#### `lib/screens/edit_*_screen.dart` fajlovi
```dart
// TODO: IMPLEMENTIRATI U SVIM EDIT SCREEN-OVIMA
// Pattern za sve edit screen-ove:

void _saveChanges() async {
  if (!_hasChanges) return;
  
  // → IMPLEMENTIRATI API calls:
  // edit_personal_info_screen.dart: PUT /api/users/{userId}/personal-info
  // edit_location_screen.dart: PUT /api/users/{userId}/location  
  // edit_education_screen.dart: PUT /api/users/{userId}/education
  // edit_work_experience_screen.dart: PUT /api/users/{userId}/work-experience
  // edit_languages_screen.dart: PUT /api/users/{userId}/languages
  // edit_driving_license_screen.dart: PUT /api/users/{userId}/driving-licenses
  
  Navigator.pop(context, true); // Return success
}
```

---

## 🚨 PRIORITET 2 - INVITATIONS

#### `lib/screens/invitations_screen.dart`
```dart
// TODO: IMPLEMENTIRATI - LINES 15-35
Future<void> _loadInvitations() async {
  // → IMPLEMENTIRATI: GET /api/users/{userId}/invitations
  // final response = await api.get('/api/users/$userId/invitations');
  // setState(() { invitations = response.data; });
}

// TODO: IMPLEMENTIRATI - LINE 120
void _respondToInvitation(String invitationId, String response) async {
  // → IMPLEMENTIRATI: PUT /api/invitations/{invitationId}/respond
  // final result = await api.put('/api/invitations/$invitationId/respond', {
  //   'response': response, // 'accept' ili 'reject'
  // });
  // 
  // if (result.success && response == 'accept') {
  //   Navigate to job details or application
  // }
  // 
  // Refresh invitations list
  _loadInvitations();
}
```

---

## 🚨 PRIORITET 3 - DASHBOARD & STATS

#### `lib/screens/home_screen.dart`
```dart
// TODO: IMPLEMENTIRATI - LINES 20-60
class _HomeScreenState extends State<HomeScreen> {
  Map<String, dynamic> dashboardStats = {};
  List<Map<String, dynamic>> recentActivity = [];
  
  @override
  void initState() {
    super.initState();
    _loadDashboardData();
  }
  
  Future<void> _loadDashboardData() async {
    // → IMPLEMENTIRATI: GET /api/users/{userId}/dashboard
    // final response = await api.get('/api/users/$userId/dashboard');
    // setState(() {
    //   dashboardStats = response.data['stats']; // Profile completion, aplikacije, pozivnice
    //   recentActivity = response.data['recent_activity'];
    // });
  }
}
```

---

## 🔧 TEHNIČKI SETUP

### **1. HTTP Client Setup**
```dart
// Create lib/services/api_service.dart
class ApiService {
  static const String baseUrl = 'https://your-api-domain.com/api';
  final Dio _dio = Dio();
  
  ApiService() {
    _dio.options.baseUrl = baseUrl;
    _dio.interceptors.add(AuthInterceptor()); // Add auth token
  }
  
  // Implement GET, POST, PUT, DELETE methods
}
```

### **2. Auth Token Management**  
```dart
// Create lib/services/auth_service.dart
class AuthService {
  static const String _tokenKey = 'access_token';
  
  Future<void> saveToken(String token) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_tokenKey, token);
  }
  
  Future<String?> getToken() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_tokenKey);
  }
}
```

### **3. State Management**
```dart
// Consider implementing Provider or Riverpod for:
// - User state management
// - Job listings state
// - Chat messages state
// - Application status updates
```

### **4. Real-time Features**
```dart
// For chat functionality, implement:
// - WebSocket connection service
// - Message state management
// - Connection retry logic
// - Typing indicators
```

---

## 📋 CHECKLIST ZA DEVELOPERA

### **Phase 1 - Authentication & Core**
- [ ] Implement registration API call
- [ ] Implement login API call  
- [ ] Setup token storage
- [ ] Implement onboarding data submission
- [ ] Add API error handling

### **Phase 2 - Job Features**
- [ ] Implement job listings API
- [ ] Implement job details API
- [ ] Implement job application API
- [ ] Add job filtering
- [ ] Add pagination

### **Phase 3 - User Management**
- [ ] Implement profile loading
- [ ] Implement profile updates
- [ ] Implement image upload
- [ ] Add profile validation

### **Phase 4 - Communication**
- [ ] Implement conversation loading
- [ ] Implement messaging API
- [ ] Setup WebSocket connections
- [ ] Add real-time message updates
- [ ] Implement invitation management

### **Phase 5 - Advanced Features** 
- [ ] Add push notifications
- [ ] Implement search functionality
- [ ] Add analytics tracking
- [ ] Performance optimizations

**Svaki TODO komentar u kodu označava točno gdje treba implementirati API logic i zamijeniti mock podatke.**