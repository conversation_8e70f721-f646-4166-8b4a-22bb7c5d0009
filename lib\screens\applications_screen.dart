import 'package:flutter/material.dart';
import '../constants/design_constants.dart';
import '../utils/navigation_service.dart';

class ApplicationsScreen extends StatefulWidget {
  @override
  _ApplicationsScreenState createState() => _ApplicationsScreenState();
}

class _ApplicationsScreenState extends State<ApplicationsScreen> {
  String _activeTab = 'review';

  final List<Map<String, dynamic>> _allApplications = [
    {
      'id': '1',
      'jobTitle': 'Senior Software Engineer',
      'company': 'TechCorp GmbH',
      'applicationDate': '15. decembar 2024',
      'status': 'interview',
      'salary': '€4,500 - €6,000',
      'logo': '🏢'
    },
    {
      'id': '2',
      'jobTitle': 'Frontend Developer',
      'company': 'WebSolutions',
      'applicationDate': '12. decembar 2024',
      'status': 'review',
      'salary': '€3,200 - €4,500',
      'logo': '🏢'
    },
    {
      'id': '3',
      'jobTitle': 'Marketing Manager',
      'company': 'Alpine Solutions',
      'applicationDate': '10. decembar 2024',
      'status': 'onboarding',
      'salary': '€3,800 - €5,200',
      'logo': '🏢'
    },
    {
      'id': '4',
      'jobTitle': 'Data Analyst',
      'company': 'DataFlow AG',
      'applicationDate': '8. decembar 2024',
      'status': 'initial_rejected',
      'salary': '€3,000 - €4,200',
      'logo': '🏢'
    },
    {
      'id': '5',
      'jobTitle': 'UX Designer',
      'company': 'DesignStudio',
      'applicationDate': '5. decembar 2024',
      'status': 'contract',
      'salary': '€3,500 - €4,800',
      'logo': '🏢'
    },
    {
      'id': '6',
      'jobTitle': 'Backend Developer',
      'company': 'ServerTech',
      'applicationDate': '3. decembar 2024',
      'status': 'interview_rejected',
      'salary': '€3,800 - €5,000',
      'logo': '🏢'
    },
  ];

  final List<Map<String, dynamic>> _tabConfig = [
    {
      'id': 'review',
      'label': 'Pregled',
      'icon': Icons.schedule_rounded,
      'color': const Color(0xFFF59E0B),
      'bgColor': const Color(0xFFFEF3C7),
      'borderColor': const Color(0xFFF59E0B),
      'hint': 'Kompanije zaprimile Vašu prijavu. Čekamo odluku.'
    },
    {
      'id': 'interview',
      'label': 'Intervju',
      'icon': Icons.people_rounded,
      'color': const Color(0xFF3B82F6),
      'bgColor': const Color(0xFFDEEEFF),
      'borderColor': const Color(0xFF3B82F6),
      'hint': 'Ove kompanije Vas žele upoznati.'
    },
    {
      'id': 'contract',
      'label': 'Ugovor',
      'icon': Icons.description_rounded,
      'color': const Color(0xFF10B981),
      'bgColor': const Color(0xFFD1FAE5),
      'borderColor': const Color(0xFF10B981),
      'hint': 'Čestitamo! Kompanije vam nude ugovor o radu.'
    },
    {
      'id': 'onboarding',
      'label': 'Početak rada',
      'icon': Icons.flag_rounded,
      'color': const Color(0xFF7C3AED),
      'bgColor': const Color(0xFFE9D5FF),
      'borderColor': const Color(0xFF7C3AED),
      'hint': 'Dogovarate početak rada, radne dozvole i vize.'
    },
    {
      'id': 'initial_rejected',
      'label': 'Odbijeno',
      'icon': Icons.cancel_rounded,
      'color': const Color(0xFFEF4444),
      'bgColor': const Color(0xFFFEE2E2),
      'borderColor': const Color(0xFFEF4444),
      'hint': 'Ove prijave su odbijene u početnoj fazi.'
    },
    {
      'id': 'interview_rejected',
      'label': 'Nije odabrano',
      'icon': Icons.thumb_down_rounded,
      'color': const Color(0xFF8B5A2B),
      'bgColor': const Color(0xFFF3E8D3),
      'borderColor': const Color(0xFF8B5A2B),
      'hint': 'Intervju je završen, ali niste odabrani za poziciju.'
    },
  ];

  List<Map<String, dynamic>> _getApplicationsByStatus(String status) {
    return _allApplications.where((app) => app['status'] == status).toList();
  }

  Map<String, dynamic>? _getStatusConfig(String status) {
    return _tabConfig.firstWhere((tab) => tab['id'] == status, orElse: () => _tabConfig[0]);
  }

  Map<String, dynamic>? _getActiveTabConfig() {
    return _tabConfig.firstWhere((tab) => tab['id'] == _activeTab, orElse: () => _tabConfig[0]);
  }

  @override
  Widget build(BuildContext context) {
    final filteredApplications = _getApplicationsByStatus(_activeTab);
    
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            DesignConstants.backgroundLight,
            DesignConstants.white,
          ],
        ),
      ),
      child: Column(
        children: [
          Container(
            color: Colors.white,
            child: SafeArea(
              bottom: false,
              child: Container(
                padding: EdgeInsets.only(bottom: DesignConstants.spaceM),
                decoration: BoxDecoration(
                  color: Colors.white,
                  border: Border(
                    bottom: BorderSide(
                      color: DesignConstants.borderGray,
                      width: 1,
                    ),
                  ),
                  boxShadow: DesignConstants.shadowSmall,
                ),
                child: Column(
                  children: [
                    // Tab Navigation
                    Padding(
                      padding: EdgeInsets.fromLTRB(DesignConstants.spaceXL, DesignConstants.spaceL, DesignConstants.spaceXL, 0),
                      child: SingleChildScrollView(
                        scrollDirection: Axis.horizontal,
                        child: Row(
                          children: _tabConfig.map((tab) {
                            final isActive = _activeTab == tab['id'];
                            return Container(
                              margin: EdgeInsets.only(right: DesignConstants.spaceS),
                              child: GestureDetector(
                                onTap: () => setState(() => _activeTab = tab['id']),
                                child: Container(
                                  padding: EdgeInsets.symmetric(
                                    vertical: DesignConstants.spaceS + 2, 
                                    horizontal: DesignConstants.spaceL
                                  ),
                                  decoration: BoxDecoration(
                                    color: isActive ? tab['bgColor'] : DesignConstants.backgroundLight,
                                    borderRadius: BorderRadius.circular(DesignConstants.radiusMedium),
                                    border: Border.all(
                                      color: isActive ? tab['color'] : DesignConstants.borderGray,
                                    ),
                                    boxShadow: isActive ? DesignConstants.shadowMedium : null,
                                  ),
                                  child: Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      Icon(
                                        tab['icon'],
                                        size: DesignConstants.iconSmall,
                                        color: isActive ? tab['color'] : DesignConstants.textSecondary,
                                      ),
                                      SizedBox(width: DesignConstants.spaceS - 2),
                                      Text(
                                        tab['label'],
                                        style: TextStyle(
                                          fontSize: 12,
                                          fontWeight: FontWeight.w600,
                                          color: isActive ? tab['color'] : DesignConstants.textSecondary,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            );
                          }).toList(),
                        ),
                      ),
                    ),
                    // Hint Text
                    if (_getActiveTabConfig()?['hint'] != null)
                      Container(
                        width: double.infinity,
                        margin: EdgeInsets.fromLTRB(DesignConstants.spaceXL, DesignConstants.spaceM, DesignConstants.spaceXL, DesignConstants.spaceM),
                        padding: EdgeInsets.all(DesignConstants.spaceL),
                        decoration: BoxDecoration(
                          color: _getActiveTabConfig()!['color'].withOpacity(0.05),
                          borderRadius: BorderRadius.circular(DesignConstants.radiusMedium),
                          border: Border.all(
                            color: _getActiveTabConfig()!['color'].withOpacity(0.2),
                          ),
                        ),
                        child: Row(
                          children: [
                            Icon(
                              Icons.info_outline,
                              size: DesignConstants.iconSmall,
                              color: _getActiveTabConfig()!['color'],
                            ),
                            SizedBox(width: DesignConstants.spaceS),
                            Expanded(
                              child: Text(
                                _getActiveTabConfig()!['hint'],
                                style: DesignConstants.bodyXSmall.copyWith(
                                  color: DesignConstants.textSecondary,
                                  height: 1.4,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                  ],
                ),
              ),
            ),
          ),
          
          Expanded(
            child: filteredApplications.isNotEmpty
                ? _buildApplicationList(filteredApplications)
                : _buildEmptyState(),
          ),
        ],
      ),
    );
  }

  Widget _buildApplicationList(List<Map<String, dynamic>> applications) {
    final activeTabConfig = _getActiveTabConfig();
    final activeTabColor = activeTabConfig!['color'];
    
    return ListView.builder(
      padding: EdgeInsets.fromLTRB(DesignConstants.spaceXL, DesignConstants.spaceL, DesignConstants.spaceXL, 80),
      itemCount: applications.length,
      itemBuilder: (context, index) {
        final application = applications[index];
        final statusConfig = _getStatusConfig(application['status'])!;
        
        return Container(
          margin: EdgeInsets.only(bottom: DesignConstants.spaceL),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                DesignConstants.white,
                activeTabColor.withOpacity(0.01),
              ],
            ),
            borderRadius: BorderRadius.circular(DesignConstants.radiusXLarge),
            border: Border.all(
              color: activeTabColor.withOpacity(0.08),
              width: 1.5,
            ),
            boxShadow: [
              BoxShadow(
                color: activeTabColor.withOpacity(0.04),
                blurRadius: 12,
                offset: Offset(0, 4),
                spreadRadius: 1,
              ),
              BoxShadow(
                color: Colors.black.withOpacity(0.03),
                blurRadius: 8,
                offset: Offset(0, 2),
              ),
            ],
          ),
          child: Material(
            color: Colors.transparent,
            borderRadius: BorderRadius.circular(DesignConstants.radiusXLarge),
            child: InkWell(
              borderRadius: BorderRadius.circular(DesignConstants.radiusXLarge),
              onTap: () {
                NavigationService().navigateToScreen('application_details', data: {
                  'application': application,
                });
              },
              child: Padding(
                padding: EdgeInsets.all(DesignConstants.spaceXL),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Container(
                          width: 56,
                          height: 56,
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              begin: Alignment.topLeft,
                              end: Alignment.bottomRight,
                              colors: [
                                activeTabColor.withOpacity(0.1),
                                activeTabColor.withOpacity(0.05),
                              ],
                            ),
                            borderRadius: BorderRadius.circular(DesignConstants.radiusLarge),
                            border: Border.all(
                              color: activeTabColor.withOpacity(0.15),
                              width: 1,
                            ),
                          ),
                          child: Icon(
                            statusConfig['icon'],
                            color: activeTabColor,
                            size: DesignConstants.iconLarge,
                          ),
                        ),
                        SizedBox(width: DesignConstants.spaceL),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                application['jobTitle'],
                                style: DesignConstants.titleSmall.copyWith(
                                  color: DesignConstants.black,
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                              SizedBox(height: DesignConstants.spaceXS),
                              Text(
                                application['company'],
                                style: DesignConstants.bodyMedium.copyWith(
                                  color: activeTabColor,
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: DesignConstants.spaceM),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Row(
                          children: [
                            Icon(
                              Icons.calendar_today_outlined,
                              size: DesignConstants.iconSmall - 2,
                              color: DesignConstants.textSecondary,
                            ),
                            SizedBox(width: DesignConstants.spaceXS),
                            Text(
                              'Poslano ${application['applicationDate']}',
                              style: DesignConstants.captionMedium.copyWith(
                                color: DesignConstants.darkGray,
                              ),
                            ),
                          ],
                        ),
                        Text(
                          application['salary'] ?? '€3,000 - €4,000',
                          style: DesignConstants.labelMedium.copyWith(
                            fontWeight: FontWeight.w600,
                            color: activeTabColor,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildEmptyState() {
    final activeConfig = _tabConfig.firstWhere((tab) => tab['id'] == _activeTab);
    
    String title, description;
    switch (_activeTab) {
      case 'review':
        title = 'Nema prijava na pregledu';
        description = 'Prijave koje šaljete će se prikazati ovdje';
        break;
      case 'interview':
        title = 'Nema poziva za intervju';
        description = 'Prijave za koje ste pozvani na intervju će se pojaviti ovdje';
        break;
      case 'contract':
        title = 'Nema ponuda za ugovor';
        description = 'Ponude za ugovor o radu će se prikazati ovdje';
        break;
      case 'onboarding':
        title = 'Nema aktivnih početaka rada';
        description = 'Procesi početka rada i dobijanja dozvola će se prikazati ovdje';
        break;
      case 'initial_rejected':
        title = 'Nema odbijenih prijava';
        description = 'Odbijene prijave će biti prikazane ovdje';
        break;
      case 'interview_rejected':
        title = 'Nema neodabranih prijava';
        description = 'Prijave gdje niste odabrani nakon intervjua će se prikazati ovdje';
        break;
      default:
        title = 'Nema prijava';
        description = 'Vaše prijave će se prikazati ovdje';
    }
    
    return Center(
      child: Padding(
        padding: EdgeInsets.all(DesignConstants.spaceXXL + DesignConstants.spaceL),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    DesignConstants.modernPrimaryBlue.withOpacity(0.1),
                    DesignConstants.modernPrimaryBlue.withOpacity(0.05),
                  ],
                ),
                shape: BoxShape.circle,
                border: Border.all(
                  color: DesignConstants.modernPrimaryBlue.withOpacity(0.2),
                  width: 2,
                ),
                boxShadow: DesignConstants.shadowSmall,
              ),
              child: Icon(
                activeConfig['icon'],
                size: DesignConstants.iconXLarge,
                color: DesignConstants.modernPrimaryBlue,
              ),
            ),
            SizedBox(height: DesignConstants.spaceXXL),
            Text(
              title,
              style: DesignConstants.titleMedium.copyWith(
                color: DesignConstants.black,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: DesignConstants.spaceS),
            Text(
              description,
              style: DesignConstants.bodyMedium.copyWith(
                color: DesignConstants.darkGray,
                height: 1.4,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

}