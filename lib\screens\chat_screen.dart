import 'package:flutter/material.dart';
import '../constants/design_constants.dart';

class ChatScreen extends StatefulWidget {
  final String companyName;
  final String companyLogo;
  final String jobTitle;
  final String applicationStatus;
  final String? salary;

  const ChatScreen({
    Key? key,
    required this.companyName,
    required this.companyLogo,
    this.jobTitle = 'Senior Software Engineer',
    this.applicationStatus = 'interview',
    this.salary,
  }) : super(key: key);

  @override
  _ChatScreenState createState() => _ChatScreenState();
}

class _ChatScreenState extends State<ChatScreen> {
  final TextEditingController _messageController = TextEditingController();
  final ScrollController _scrollController = ScrollController();

  final List<ChatMessage> _messages = [
    ChatMessage(
      text: 'Pozdrav! Interesuje nas vaš profil za poziciju Senior Developer-a. Možemo li da zakazemo razgovor?',
      isFromCompany: true,
      timestamp: DateTime.now().subtract(Duration(hours: 2)),
    ),
    ChatMessage(
      text: 'Zdravo! Hvala vam na interesovanju. Rado bih čuo više detalja o poziciji.',
      isFromCompany: false,
      timestamp: DateTime.now().subtract(Duration(hours: 1, minutes: 30)),
    ),
    ChatMessage(
      text: 'Odlično! Pozicija je za rad u našem timu backend developera. Koristimode Node.js i TypeScript. Da li imate iskustva sa ovim tehnologijama?',
      isFromCompany: true,
      timestamp: DateTime.now().subtract(Duration(minutes: 45)),
    ),
    ChatMessage(
      text: 'Da, imam preko 3 godine iskustva sa Node.js i TypeScript. Takođe radim i sa MongoDB i PostgreSQL.',
      isFromCompany: false,
      timestamp: DateTime.now().subtract(Duration(minutes: 30)),
    ),
  ];

  @override
  void dispose() {
    _messageController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  Map<String, dynamic> _getStatusConfig(String status) {
    switch (status) {
      case 'review':
        return {
          'label': 'Pregled',
          'color': const Color(0xFFF59E0B),
          'bgColor': const Color(0xFFFEF3C7),
          'icon': Icons.schedule_rounded,
        };
      case 'interview':
        return {
          'label': 'Intervju',
          'color': const Color(0xFF3B82F6),
          'bgColor': const Color(0xFFDEEEFF),
          'icon': Icons.people_rounded,
        };
      case 'contract':
        return {
          'label': 'Ugovor',
          'color': const Color(0xFF10B981),
          'bgColor': const Color(0xFFD1FAE5),
          'icon': Icons.description_rounded,
        };
      case 'onboarding':
        return {
          'label': 'Početak rada',
          'color': const Color(0xFF7C3AED),
          'bgColor': const Color(0xFFE9D5FF),
          'icon': Icons.flag_rounded,
        };
      case 'initial_rejected':
        return {
          'label': 'Odbijeno',
          'color': const Color(0xFFEF4444),
          'bgColor': const Color(0xFFFEE2E2),
          'icon': Icons.cancel_rounded,
        };
      case 'interview_rejected':
        return {
          'label': 'Nije odabrano',
          'color': const Color(0xFF8B5A2B),
          'bgColor': const Color(0xFFF3E8D3),
          'icon': Icons.thumb_down_rounded,
        };
      default:
        return {
          'label': 'Pregled',
          'color': const Color(0xFFF59E0B),
          'bgColor': const Color(0xFFFEF3C7),
          'icon': Icons.schedule_rounded,
        };
    }
  }

  void _sendMessage() {
    if (_messageController.text.trim().isEmpty) return;

    setState(() {
      _messages.add(
        ChatMessage(
          text: _messageController.text,
          isFromCompany: false,
          timestamp: DateTime.now(),
        ),
      );
    });

    _messageController.clear();
    
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _scrollController.animateTo(
        _scrollController.position.maxScrollExtent,
        duration: Duration(milliseconds: 300),
        curve: Curves.easeOut,
      );
    });
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            DesignConstants.backgroundLight,
            DesignConstants.white,
          ],
        ),
      ),
      child: Scaffold(
        backgroundColor: Colors.transparent,
        appBar: AppBar(
          backgroundColor: DesignConstants.white.withOpacity(0.95),
          elevation: 0,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back_ios,
            color: const Color(0xFF0F172A),
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Row(
          children: [
            Container(
              width: 36,
              height: 36,
              decoration: BoxDecoration(
                color: const Color(0xFF1E3A8A).withOpacity(0.1),
                borderRadius: BorderRadius.circular(18),
              ),
              child: Center(
                child: Text(
                  widget.companyLogo,
                  style: TextStyle(fontSize: 16),
                ),
              ),
            ),
            SizedBox(width: 12),
            Expanded(
              child: Text(
                widget.companyName,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: const Color(0xFF0F172A),
                ),
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
        actions: [
          IconButton(
            icon: Icon(
              Icons.more_vert,
              color: const Color(0xFF64748B),
            ),
            onPressed: () {
              // Show options menu
            },
          ),
        ],
        bottom: PreferredSize(
          preferredSize: Size.fromHeight(1),
          child: Container(
            height: 1,
            color: const Color(0xFFE2E8F0),
          ),
        ),
      ),
      body: Column(
        children: [
          _buildJobInfoBar(),
          Expanded(
            child: ListView.builder(
              controller: _scrollController,
              padding: EdgeInsets.all(16),
              itemCount: _messages.length,
              itemBuilder: (context, index) {
                return _buildMessageBubble(_messages[index]);
              },
            ),
          ),
          _buildMessageInput(),
        ],
      ),
    )
    );
  }

  Widget _buildJobInfoBar() {
    final statusConfig = _getStatusConfig(widget.applicationStatus);
    
    return Container(
      margin: EdgeInsets.fromLTRB(DesignConstants.spaceL, DesignConstants.spaceS, DesignConstants.spaceL, DesignConstants.spaceS),
      padding: EdgeInsets.all(DesignConstants.spaceM),
      decoration: BoxDecoration(
        color: DesignConstants.white,
        borderRadius: BorderRadius.circular(DesignConstants.radiusMedium),
        border: Border.all(
          color: DesignConstants.borderGray.withOpacity(0.3),
          width: 1,
        ),
        boxShadow: DesignConstants.shadowSmall,
      ),
      child: Row(
        children: [
          Icon(
            Icons.work_outline_rounded,
            size: DesignConstants.iconSmall,
            color: DesignConstants.textSecondary,
          ),
          SizedBox(width: DesignConstants.spaceS),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  widget.jobTitle,
                  style: DesignConstants.bodyMedium.copyWith(
                    fontWeight: FontWeight.w600,
                    color: DesignConstants.black,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                SizedBox(height: 2),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      widget.salary ?? '€4,500 - €6,000',
                      style: DesignConstants.captionSmall.copyWith(
                        fontWeight: FontWeight.w600,
                        color: DesignConstants.textSecondary,
                      ),
                    ),
                    Container(
                      padding: EdgeInsets.symmetric(
                        horizontal: DesignConstants.spaceXS,
                        vertical: 2,
                      ),
                      decoration: BoxDecoration(
                        color: statusConfig['color'].withOpacity(0.1),
                        borderRadius: BorderRadius.circular(DesignConstants.radiusSmall),
                      ),
                      child: Text(
                        statusConfig['label'],
                        style: DesignConstants.captionSmall.copyWith(
                          fontWeight: FontWeight.w600,
                          color: statusConfig['color'],
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMessageBubble(ChatMessage message) {
    return Padding(
      padding: EdgeInsets.only(bottom: 12),
      child: Row(
        mainAxisAlignment: message.isFromCompany 
            ? MainAxisAlignment.start 
            : MainAxisAlignment.end,
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          if (message.isFromCompany) ...[
            Container(
              width: 32,
              height: 32,
              decoration: BoxDecoration(
                color: const Color(0xFF1E3A8A).withOpacity(0.1),
                borderRadius: BorderRadius.circular(16),
              ),
              child: Center(
                child: Text(
                  widget.companyLogo,
                  style: TextStyle(fontSize: 14),
                ),
              ),
            ),
            SizedBox(width: 8),
          ],
          Flexible(
            child: Container(
              constraints: BoxConstraints(
                maxWidth: MediaQuery.of(context).size.width * 0.75,
              ),
              padding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              decoration: BoxDecoration(
                color: message.isFromCompany 
                    ? Colors.white
                    : const Color(0xFF1E3A8A),
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(20),
                  topRight: Radius.circular(20),
                  bottomLeft: message.isFromCompany 
                      ? Radius.circular(4) 
                      : Radius.circular(20),
                  bottomRight: message.isFromCompany 
                      ? Radius.circular(20) 
                      : Radius.circular(4),
                ),
                border: message.isFromCompany
                    ? Border.all(
                        color: const Color(0xFFE2E8F0),
                        width: 1,
                      )
                    : null,
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    message.text,
                    style: TextStyle(
                      fontSize: 14,
                      color: message.isFromCompany 
                          ? const Color(0xFF0F172A)
                          : Colors.white,
                      fontWeight: FontWeight.w400,
                      height: 1.4,
                    ),
                  ),
                  SizedBox(height: 4),
                  Text(
                    _formatTimestamp(message.timestamp),
                    style: TextStyle(
                      fontSize: 11,
                      color: message.isFromCompany 
                          ? const Color(0xFF64748B)
                          : Colors.white.withOpacity(0.8),
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMessageInput() {
    return Container(
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(
          top: BorderSide(
            color: const Color(0xFFE2E8F0),
            width: 1,
          ),
        ),
      ),
      child: SafeArea(
        top: false,
        child: Row(
          children: [
            Expanded(
              child: Container(
                decoration: BoxDecoration(
                  color: const Color(0xFFF8FAFC),
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(
                    color: const Color(0xFFE2E8F0),
                    width: 1,
                  ),
                ),
                child: TextField(
                  controller: _messageController,
                  decoration: InputDecoration(
                    hintText: 'Napišite poruku...',
                    hintStyle: TextStyle(
                      color: const Color(0xFF64748B),
                      fontSize: 14,
                    ),
                    border: InputBorder.none,
                    contentPadding: EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 12,
                    ),
                  ),
                  style: TextStyle(
                    color: const Color(0xFF0F172A),
                    fontSize: 14,
                  ),
                  maxLines: null,
                  textCapitalization: TextCapitalization.sentences,
                ),
              ),
            ),
            SizedBox(width: 8),
            Container(
              decoration: BoxDecoration(
                color: const Color(0xFF1E3A8A),
                borderRadius: BorderRadius.circular(20),
              ),
              child: Material(
                color: Colors.transparent,
                child: InkWell(
                  borderRadius: BorderRadius.circular(20),
                  onTap: _sendMessage,
                  child: Container(
                    padding: EdgeInsets.all(12),
                    child: Icon(
                      Icons.send_rounded,
                      color: Colors.white,
                      size: 20,
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  String _formatTimestamp(DateTime timestamp) {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inDays > 0) {
      return '${difference.inDays}d';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m';
    } else {
      return 'Sada';
    }
  }
}

class ChatMessage {
  final String text;
  final bool isFromCompany;
  final DateTime timestamp;

  ChatMessage({
    required this.text,
    required this.isFromCompany,
    required this.timestamp,
  });
}