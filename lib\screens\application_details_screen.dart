import 'package:flutter/material.dart';
import '../constants/design_constants.dart';
import '../widgets/timeline_widget.dart';
import '../utils/navigation_service.dart';

class ApplicationDetailsScreen extends StatefulWidget {
  final Map<String, dynamic> application;

  const ApplicationDetailsScreen({
    Key? key,
    required this.application,
  }) : super(key: key);

  @override
  _ApplicationDetailsScreenState createState() => _ApplicationDetailsScreenState();
}

class _ApplicationDetailsScreenState extends State<ApplicationDetailsScreen> {

  @override
  Widget build(BuildContext context) {
    final status = widget.application['status'];

    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            DesignConstants.backgroundLight,
            DesignConstants.white,
          ],
        ),
      ),
      child: Column(
        children: [
          Expanded(
            child: SingleChildScrollView(
              padding: EdgeInsets.only(bottom: status == 'contract' ? 100 : 20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildJobCard(),
                  _buildTimelineSection(),
                  if (status == 'review') _buildPendingInfo(),
                  if (status == 'interview') _buildInterviewInfo(),
                  if (status == 'onboarding') _buildOnboardingInfo(),
                ],
              ),
            ),
          ),
          if (status == 'contract') _buildActionButtons(),
        ],
      ),
    );
  }


  Widget _buildJobCard() {
    return Container(
      margin: EdgeInsets.fromLTRB(DesignConstants.spaceXL, DesignConstants.spaceL, DesignConstants.spaceXL, 0),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(DesignConstants.radiusXLarge),
        border: Border.all(
          color: DesignConstants.borderGray.withOpacity(0.3),
          width: 1,
        ),
        boxShadow: DesignConstants.modernGlassShadow,
      ),
      child: Material(
        color: Colors.transparent,
        borderRadius: BorderRadius.circular(DesignConstants.radiusXLarge),
        child: InkWell(
          borderRadius: BorderRadius.circular(DesignConstants.radiusXLarge),
          onTap: () {
            // Navigate to AppliedJobDetailsScreen
            NavigationService().navigateToScreen('applied_job_details', data: {
              'job': {
                'title': widget.application['jobTitle'],
                'company': widget.application['company'],
                'location': 'Berlin, Njemačka',
                'type': 'Puno radno vrijeme',
                'salary': '€3.500-4.500',
                'posted': 'Objavljeno prije 5 dana',
                'description': '''Tražimo iskusnog ${widget.application['jobTitle']}-a koji će se pridružiti našem dinamičnom timu. Radićete na razvoju inovativnih softverskih rješenja koristeći najnovije tehnologije.

Bit ćete odgovorni za:
• Razvoj i održavanje aplikacija
• Colaboraciju s cross-functional timovima  
• Mentoriranje mladjih članova tima
• Učešće u arhitekturnim odlukama

Nudimo odličnu radnu atmosferu, fleksibilno radno vrijeme i mogućnosti profesionalnog razvoja.''',
                'companyInfo': '''${widget.application['company']} je vodeća kompanija osnovana u Berlinu. Specijalizujemo se za razvoj inovativnih rješenja za klijente širom Europe.

Naš tim broji preko 100 stručnjaka iz različitih oblasti, a fokus je na kreiranju kvalitetnih proizvoda koristeći najnovije tehnologije.''',
              },
              'application': widget.application,
            });
          },
          child: Padding(
            padding: EdgeInsets.all(DesignConstants.spaceXL),
            child: Row(
              children: [
                Container(
                  width: 56,
                  height: 56,
                  decoration: BoxDecoration(
                    color: DesignConstants.modernPrimaryBlue.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(DesignConstants.radiusMedium),
                  ),
                  child: Icon(
                    Icons.business,
                    color: DesignConstants.modernPrimaryBlue,
                    size: 28,
                  ),
                ),
                SizedBox(width: DesignConstants.spaceL),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        widget.application['jobTitle'],
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.w600,
                          color: DesignConstants.textPrimary,
                        ),
                      ),
                      SizedBox(height: DesignConstants.spaceXS),
                      Text(
                        widget.application['company'],
                        style: TextStyle(
                          fontSize: 16,
                          color: DesignConstants.textSecondary,
                        ),
                      ),
                    ],
                  ),
                ),
                Icon(
                  Icons.arrow_forward_ios,
                  size: DesignConstants.iconSmall,
                  color: DesignConstants.textSecondary,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildTimelineSection() {
    return Container(
      margin: EdgeInsets.fromLTRB(DesignConstants.spaceXL, DesignConstants.spaceXXL, DesignConstants.spaceXL, 0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(height: DesignConstants.spaceL),
          Container(
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(DesignConstants.radiusXLarge),
              border: Border.all(
                color: DesignConstants.borderGray.withOpacity(0.2),
                width: 1,
              ),
              boxShadow: DesignConstants.modernButtonShadow,
            ),
            child: ApplicationTimeline(
              status: widget.application['status'],
              applicationDate: widget.application['applicationDate'],
              application: widget.application,
            ),
          ),
        ],
      ),
    );
  }


  Widget _buildPendingInfo() {
    return Container(
      margin: EdgeInsets.fromLTRB(DesignConstants.spaceXL, DesignConstants.spaceXXL, DesignConstants.spaceXL, 0),
      padding: EdgeInsets.all(DesignConstants.spaceL),
      decoration: BoxDecoration(
        color: DesignConstants.accentOrange.withOpacity(0.08),
        borderRadius: BorderRadius.circular(DesignConstants.radiusMedium),
        border: Border.all(
          color: DesignConstants.accentOrange.withOpacity(0.25),
        ),
        boxShadow: DesignConstants.shadowMedium,
      ),
      child: Row(
        children: [
          Icon(
            Icons.schedule_rounded,
            color: DesignConstants.accentOrange,
            size: DesignConstants.iconMedium,
          ),
          SizedBox(width: DesignConstants.spaceM),
          Expanded(
            child: Text(
              'Vaša prijava je na pregledu. Kompanija će vas kontaktirati uskoro.',
              style: TextStyle(
                fontSize: 14,
                color: DesignConstants.accentOrange.withOpacity(0.95),
                height: 1.4,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInterviewInfo() {
    return Container(
      margin: EdgeInsets.fromLTRB(DesignConstants.spaceXL, DesignConstants.spaceXXL, DesignConstants.spaceXL, 0),
      padding: EdgeInsets.all(DesignConstants.spaceL),
      decoration: BoxDecoration(
        color: DesignConstants.secondaryBlue.withOpacity(0.08),
        borderRadius: BorderRadius.circular(DesignConstants.radiusMedium),
        border: Border.all(
          color: DesignConstants.secondaryBlue.withOpacity(0.25),
        ),
        boxShadow: DesignConstants.shadowMedium,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.people_rounded,
                color: DesignConstants.secondaryBlue,
                size: DesignConstants.iconMedium,
              ),
              SizedBox(width: DesignConstants.spaceM),
              Expanded(
                child: Text(
                  'Poziv za intervju',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: DesignConstants.modernPrimaryBlue,
                  ),
                ),
              ),
            ],
          ),
          SizedBox(height: DesignConstants.spaceS),
          Text(
            'Čestitamo! ${widget.application['company']} vas poziva na intervju. Možete ih kontaktirati za dodatne detalje.',
            style: TextStyle(
              fontSize: 14,
              color: DesignConstants.modernPrimaryBlue,
              height: 1.4,
            ),
          ),
          SizedBox(height: DesignConstants.spaceL),
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: () {
                _showContactDialog();
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: DesignConstants.modernPrimaryBlue,
                foregroundColor: Colors.white,
                elevation: 0,
                padding: EdgeInsets.symmetric(vertical: DesignConstants.spaceM),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(DesignConstants.radiusLarge),
                ),
                shadowColor: DesignConstants.modernPrimaryBlue.withOpacity(0.3),
              ).copyWith(
                elevation: MaterialStateProperty.all(8),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(Icons.chat_bubble_outline, size: DesignConstants.iconSmall),
                  SizedBox(width: DesignConstants.spaceS),
                  Text(
                    'Kontaktiraj poslodavca',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons() {
    return Container(
      color: Colors.white,
      child: SafeArea(
        child: Container(
          padding: EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: Colors.white,
            border: Border(
              top: BorderSide(
                color: const Color(0xFFE2E8F0),
                width: 1,
              ),
            ),
          ),
          child: Row(
            children: [
              Expanded(
                child: Container(
                  decoration: BoxDecoration(
                    boxShadow: [
                      BoxShadow(
                        color: const Color(0xFFEF4444).withOpacity(0.3),
                        blurRadius: 20,
                        offset: Offset(0, 8),
                      ),
                    ],
                  ),
                  child: OutlinedButton(
                    onPressed: () {
                      _showRejectDialog();
                    },
                    style: OutlinedButton.styleFrom(
                      backgroundColor: Colors.white,
                      side: BorderSide(color: const Color(0xFFEF4444), width: 2),
                      elevation: 0,
                      shadowColor: Colors.transparent,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(16),
                      ),
                      padding: EdgeInsets.symmetric(vertical: 16),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        SizedBox(width: 8),
                        Text(
                          'Odbijam',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                            color: const Color(0xFFEF4444),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
              SizedBox(width: 16),
              Expanded(
                child: Container(
                  decoration: BoxDecoration(
                    boxShadow: [
                      BoxShadow(
                        color: const Color(0xFF10B981).withOpacity(0.3),
                        blurRadius: 20,
                        offset: Offset(0, 8),
                      ),
                    ],
                  ),
                  child: ElevatedButton(
                    onPressed: () {
                      _showAcceptDialog();
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFF10B981),
                      foregroundColor: Colors.white,
                      elevation: 0,
                      shadowColor: Colors.transparent,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(16),
                      ),
                      padding: EdgeInsets.symmetric(vertical: 16),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        SizedBox(width: 8),
                        Text(
                          'Prihvatam',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildOnboardingInfo() {
    return Container(
      margin: EdgeInsets.fromLTRB(DesignConstants.spaceXL, DesignConstants.spaceXXL, DesignConstants.spaceXL, 0),
      padding: EdgeInsets.all(DesignConstants.spaceL),
      decoration: BoxDecoration(
        color: Color(0xFF7C3AED).withOpacity(0.08),
        borderRadius: BorderRadius.circular(DesignConstants.radiusMedium),
        border: Border.all(
          color: Color(0xFF7C3AED).withOpacity(0.25),
        ),
        boxShadow: DesignConstants.shadowMedium,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.flag_rounded,
                color: Color(0xFF7C3AED),
                size: DesignConstants.iconMedium,
              ),
              SizedBox(width: DesignConstants.spaceM),
              Expanded(
                child: Text(
                  'Početak rada - Finalne pripreme',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Color(0xFF5B21B6),
                  ),
                ),
              ),
            ],
          ),
          SizedBox(height: DesignConstants.spaceS),
          Text(
            'Čestitamo! Prihvatili ste ponudu od ${widget.application['company']}. Sada dogovarate početak rada, radne dozvole i proces dobijanja vize ako je potrebno.',
            style: TextStyle(
              fontSize: 14,
              color: Color(0xFF5B21B6),
              height: 1.4,
            ),
          ),
          SizedBox(height: DesignConstants.spaceL),
          // Changed to Column to prevent overflow
          Column(
            children: [
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: () {
                    _showContactDialog();
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Color(0xFF7C3AED),
                    foregroundColor: Colors.white,
                    elevation: 0,
                    padding: EdgeInsets.symmetric(vertical: DesignConstants.spaceM),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(DesignConstants.radiusSmall),
                    ),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(Icons.chat_bubble_outline, size: DesignConstants.iconSmall),
                      SizedBox(width: DesignConstants.spaceS),
                      Text(
                        'Kontaktiraj HR',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              SizedBox(height: DesignConstants.spaceM),
              SizedBox(
                width: double.infinity,
                child: OutlinedButton(
                  onPressed: () {
                    _navigateToSSCServices();
                  },
                  style: OutlinedButton.styleFrom(
                    side: BorderSide(color: DesignConstants.successGreen, width: 1.5),
                    padding: EdgeInsets.symmetric(vertical: DesignConstants.spaceM),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(DesignConstants.radiusSmall),
                    ),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        Icons.verified_user_outlined,
                        size: DesignConstants.iconSmall,
                        color: DesignConstants.successGreen,
                      ),
                      SizedBox(width: DesignConstants.spaceS),
                      Flexible(
                        child: Text(
                          'Treba li ti pomoć oko vize i papira?',
                          style: TextStyle(
                            color: DesignConstants.successGreen,
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  void _navigateToSSCServices() {
    NavigationService().navigateToScreen('ssc_services');
  }

  void _showContactDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        title: Row(
          children: [
            Container(
              padding: EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: const Color(0xFF3B82F6).withOpacity(0.1),
                shape: BoxShape.circle,
              ),
              child: Icon(
                Icons.chat_bubble_outline,
                color: const Color(0xFF3B82F6),
                size: 24,
              ),
            ),
            SizedBox(width: 12),
            Expanded(
              child: Text(
                'Kontaktiraj poslodavca',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: const Color(0xFF0F172A),
                ),
              ),
            ),
          ],
        ),
        content: Text(
          'Kontaktirajte ${widget.application['company']} u vezi sa pozicijom "${widget.application['jobTitle']}".',
          style: TextStyle(
            fontSize: 16,
            color: const Color(0xFF64748B),
            height: 1.5,
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'Otkaži',
              style: TextStyle(
                color: const Color(0xFF64748B),
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Row(
                    children: [
                      Icon(Icons.chat_bubble, color: Colors.white),
                      SizedBox(width: 12),
                      Text('Otvaram chat sa ${widget.application['company']}...'),
                    ],
                  ),
                  backgroundColor: const Color(0xFF3B82F6),
                  behavior: SnackBarBehavior.floating,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF3B82F6),
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: Text(
              'Otvori chat',
              style: TextStyle(fontWeight: FontWeight.w600),
            ),
          ),
        ],
      ),
    );
  }

  void _showAcceptDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        title: Row(
          children: [
            Container(
              padding: EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: const Color(0xFF10B981).withOpacity(0.1),
                shape: BoxShape.circle,
              ),
              child: Text(
                '✅',
                style: TextStyle(fontSize: 20),
              ),
            ),
            SizedBox(width: 12),
            Text(
              'Prihvati ponudu',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: const Color(0xFF0F172A),
              ),
            ),
          ],
        ),
        content: Text(
          'Da li ste sigurni da želite da prihvatite ponudu za poziciju "${widget.application['jobTitle']}" u kompaniji ${widget.application['company']}?\n\nOva akcija je konačna.',
          style: TextStyle(
            fontSize: 16,
            color: const Color(0xFF64748B),
            height: 1.5,
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'Otkaži',
              style: TextStyle(
                color: const Color(0xFF64748B),
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              NavigationService().navigateBack(); // Go back to applications
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Row(
                    children: [
                      Text('✅', style: TextStyle(fontSize: 16)),
                      SizedBox(width: 12),
                      Text('Čestitamo! Prihvatili ste ponudu.'),
                    ],
                  ),
                  backgroundColor: const Color(0xFF10B981),
                  behavior: SnackBarBehavior.floating,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF10B981),
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: Text(
              'Prihvati',
              style: TextStyle(fontWeight: FontWeight.w600),
            ),
          ),
        ],
      ),
    );
  }

  void _showRejectDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        title: Row(
          children: [
            Container(
              padding: EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: const Color(0xFFEF4444).withOpacity(0.1),
                shape: BoxShape.circle,
              ),
              child: Text(
                '❌',
                style: TextStyle(fontSize: 20),
              ),
            ),
            SizedBox(width: 12),
            Text(
              'Odbij ponudu',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: const Color(0xFF0F172A),
              ),
            ),
          ],
        ),
        content: Text(
          'Da li ste sigurni da želite da odbijete ponudu za poziciju "${widget.application['jobTitle']}" u kompaniji ${widget.application['company']}?\n\nOva akcija se ne može poništiti.',
          style: TextStyle(
            fontSize: 16,
            color: const Color(0xFF64748B),
            height: 1.5,
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'Otkaži',
              style: TextStyle(
                color: const Color(0xFF64748B),
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              NavigationService().navigateBack(); // Go back to applications
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Row(
                    children: [
                      Text('❌', style: TextStyle(fontSize: 16)),
                      SizedBox(width: 12),
                      Text('Ponuda je odbijena.'),
                    ],
                  ),
                  backgroundColor: const Color(0xFF64748B),
                  behavior: SnackBarBehavior.floating,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFFEF4444),
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: Text(
              'Odbij',
              style: TextStyle(fontWeight: FontWeight.w600),
            ),
          ),
        ],
      ),
    );
  }

}