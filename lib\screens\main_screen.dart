import 'package:flutter/material.dart';
import '../constants/design_constants.dart';
import 'home_screen.dart';
import 'jobs_screen.dart';
import 'applications_screen.dart';
import 'invitations_screen.dart';
import 'messages_screen.dart';
import 'profile_screen_new.dart';
import 'job_details_screen.dart';
import 'applied_job_details_screen.dart';
import 'application_details_screen.dart';
import 'ssc_services_screen.dart';
import 'invitation_job_details_screen.dart';
import 'invited_job_details_screen.dart';
import '../utils/navigation_service.dart';

class MainScreen extends StatefulWidget {
  @override
  _MainScreenState createState() => _MainScreenState();
}

class _MainScreenState extends State<MainScreen> {
  int _currentIndex = 0;
  
  // Navigation state for nested screens
  String _currentScreen = 'main';
  Map<String, dynamic> _screenData = {};
  
  final List<Widget> _mainScreens = [
    HomeScreen(),
    JobsScreen(),
    ApplicationsScreen(),
    InvitationsScreen(),
    MessagesScreen(),
  ];

  @override
  void initState() {
    super.initState();
    // Set up navigation service callbacks
    NavigationService().setNavigationCallbacks(
      navigateToScreen: navigateToScreen,
      navigateBack: navigateBack,
    );
  }

  // Method to navigate to nested screens
  void navigateToScreen(String screenName, {Map<String, dynamic>? data}) {
    setState(() {
      _currentScreen = screenName;
      _screenData = data ?? {};
    });
  }

  // Method to go back to main screens
  void navigateBack() {
    setState(() {
      _currentScreen = 'main';
      _screenData = {};
    });
  }

  Widget _getCurrentScreen() {
    if (_currentScreen == 'main') {
      return _mainScreens[_currentIndex];
    }
    
    switch (_currentScreen) {
      case 'job_details':
        return JobDetailsScreen();
      case 'applied_job_details':
        return AppliedJobDetailsScreen(
          job: _screenData['job'] ?? {},
          application: _screenData['application'] ?? {},
        );
      case 'application_details':
        return ApplicationDetailsScreen(
          application: _screenData['application'] ?? {},
        );
      case 'profile':
        return ProfileScreenNew();
      case 'ssc_services':
        return SSCServicesScreen();
      case 'invitation_job_details':
        return InvitationJobDetailsScreen();
      case 'invited_job_details':
        return InvitedJobDetailsScreen(
          job: _screenData['job'] ?? {},
          invitation: _screenData['invitation'] ?? {},
        );
      default:
        return _mainScreens[_currentIndex];
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.center,
            colors: [
              const Color(0xFF2665D0),
              const Color(0xFF2665D0).withOpacity(0.1),
            ],
            stops: [0.0, 0.3],
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              // Modern AppBar
              Container(
                padding: EdgeInsets.symmetric(horizontal: 20, vertical: 16),
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.15),
                  border: Border(
                    bottom: BorderSide(
                      color: Colors.white.withOpacity(0.2),
                      width: 1,
                    ),
                  ),
                ),
                child: Row(
                  children: [
                    Container(
                      height: 40,
                      padding: EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                      constraints: BoxConstraints(maxWidth: 140),
                      decoration: BoxDecoration(
                        color: Colors.white.withOpacity(0.2),
                        borderRadius: BorderRadius.circular(16),
                        border: Border.all(
                          color: Colors.white.withOpacity(0.3),
                          width: 1,
                        ),
                      ),
                      child: Image.asset(
                        'assets/images/BalkanPosao_logo.webp',
                        fit: BoxFit.contain,
                      ),
                    ),
                    Spacer(),
                    Container(
                      margin: EdgeInsets.only(right: 12),
                      padding: EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.white.withOpacity(0.15),
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: Colors.white.withOpacity(0.2),
                          width: 1,
                        ),
                      ),
                      child: Stack(
                        children: [
                          Icon(
                            Icons.notifications_outlined,
                            color: Colors.white,
                            size: 24,
                          ),
                          Positioned(
                            top: 0,
                            right: 0,
                            child: Container(
                              width: 10,
                              height: 10,
                              decoration: BoxDecoration(
                                color: const Color(0xFFEF4444),
                                shape: BoxShape.circle,
                                border: Border.all(
                                  color: Colors.white,
                                  width: 2,
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                    GestureDetector(
                      onTap: () {
                        navigateToScreen('profile');
                      },
                      child: Container(
                        padding: EdgeInsets.all(2),
                        decoration: BoxDecoration(
                          color: Colors.white.withOpacity(0.2),
                          shape: BoxShape.circle,
                          border: Border.all(
                            color: Colors.white.withOpacity(0.3),
                            width: 2,
                          ),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withOpacity(0.1),
                              blurRadius: 8,
                              offset: Offset(0, 4),
                            ),
                          ],
                        ),
                        child: CircleAvatar(
                          radius: 18,
                          backgroundColor: Colors.white,
                          child: Icon(
                            Icons.person_rounded,
                            color: const Color(0xFF2665D0),
                            size: 20,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              // Back button bar (show only for nested screens, except profile)
              if (_currentScreen != 'main' && _currentScreen != 'profile') _buildBackButtonBar(),
              // Main content with background
              Expanded(
                child: Container(
                  decoration: BoxDecoration(
                    color: const Color(0xFFFAFBFC),
                    borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(24),
                      topRight: Radius.circular(24),
                    ),
                  ),
                  child: _getCurrentScreen(),
                ),
              ),
            ],
          ),
        ),
      ),
      bottomNavigationBar: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              const Color(0xFF2665D0).withOpacity(0.95),
              const Color(0xFF1E4DB7),
            ],
          ),
          border: Border(
            top: BorderSide(
              color: Colors.white.withOpacity(0.2),
              width: 1,
            ),
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 20,
              offset: Offset(0, -5),
            ),
          ],
        ),
        child: SafeArea(
          child: Container(
            padding: EdgeInsets.symmetric(horizontal: 16, vertical: 6),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.1),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                _buildNavItem(0, Icons.home_outlined, Icons.home_rounded, 'Početna'),
                _buildNavItem(1, Icons.work_outline, Icons.work_rounded, 'Poslovi'),
                _buildNavItem(2, Icons.assignment_outlined, Icons.assignment_rounded, 'Prijave'),
                _buildNavItem(3, Icons.mail_outline, Icons.mail_rounded, 'Pozivnice'),
                _buildNavItem(4, Icons.chat_bubble_outline, Icons.chat_bubble_rounded, 'Poruke'),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildNavItem(int index, IconData outlinedIcon, IconData filledIcon, String label) {
    final isActive = _currentIndex == index && _currentScreen == 'main';
    
    return GestureDetector(
      onTap: () {
        setState(() {
          _currentIndex = index;
          _currentScreen = 'main'; // Always go back to main when tapping nav
          _screenData = {};
        });
      },
      child: AnimatedContainer(
        duration: Duration(milliseconds: 300),
        padding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color: isActive 
              ? Colors.white.withOpacity(0.2) 
              : Colors.transparent,
          borderRadius: BorderRadius.circular(16),
          border: isActive ? Border.all(
            color: Colors.white.withOpacity(0.3),
            width: 1,
          ) : null,
          boxShadow: isActive ? [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 8,
              offset: Offset(0, 4),
            ),
          ] : null,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            AnimatedContainer(
              duration: Duration(milliseconds: 300),
              child: Icon(
                isActive ? filledIcon : outlinedIcon,
                color: isActive 
                    ? Colors.white 
                    : Colors.white.withOpacity(0.7),
                size: isActive ? 26 : 24,
              ),
            ),
            SizedBox(height: 6),
            Text(
              label,
              style: TextStyle(
                fontSize: isActive ? 12 : 11,
                fontWeight: isActive ? FontWeight.w700 : FontWeight.w500,
                color: isActive 
                    ? Colors.white 
                    : Colors.white.withOpacity(0.8),
                letterSpacing: 0.2,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBackButtonBar() {
    String title = 'Nazad';
    switch (_currentScreen) {
      case 'job_details':
        title = 'Detalji posla';
        break;
      case 'applied_job_details':
        title = 'Detalji posla';
        break;
      case 'application_details':
        title = 'Detalji prijave';
        break;
      case 'profile':
        title = 'Profil';
        break;
      case 'ssc_services':
        title = 'SSC Usluge';
        break;
      case 'invitation_job_details':
        title = 'Pozivnica';
        break;
      case 'invited_job_details':
        title = 'Detalji posla';
        break;
    }

    return Container(
      padding: EdgeInsets.fromLTRB(
        DesignConstants.spaceL,
        DesignConstants.spaceM,
        DesignConstants.spaceL,
        DesignConstants.spaceL,
      ),
      decoration: BoxDecoration(
        color: DesignConstants.white,
        borderRadius: BorderRadius.only(
          bottomLeft: Radius.circular(DesignConstants.radiusXLarge),
          bottomRight: Radius.circular(DesignConstants.radiusXLarge),
        ),
        boxShadow: DesignConstants.shadowSmall,
      ),
      child: Row(
        children: [
          Container(
            width: 44,
            height: 44,
            decoration: BoxDecoration(
              color: DesignConstants.modernPrimaryBlue.withOpacity(0.1),
              borderRadius: BorderRadius.circular(DesignConstants.radiusLarge),
              border: Border.all(
                color: DesignConstants.modernPrimaryBlue.withOpacity(0.2),
                width: 1,
              ),
            ),
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                borderRadius: BorderRadius.circular(DesignConstants.radiusLarge),
                onTap: navigateBack,
                child: Icon(
                  Icons.arrow_back_ios_rounded,
                  color: DesignConstants.modernPrimaryBlue,
                  size: 20,
                ),
              ),
            ),
          ),
          SizedBox(width: DesignConstants.spaceM),
          Expanded(
            child: Text(
              title,
              style: DesignConstants.titleLarge.copyWith(
                color: DesignConstants.modernPrimaryBlue,
              ),
            ),
          ),
        ],
      ),
    );
  }
}