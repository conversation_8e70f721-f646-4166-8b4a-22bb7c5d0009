import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../utils/navigation_service.dart';
import '../constants/design_constants.dart';

class Job {
  final String id;
  final String title;
  final String company;
  final String country;
  final String city;
  final String salary;
  final String? type;
  final String logo;
  final String countryFlag;

  Job({
    required this.id,
    required this.title,
    required this.company,
    required this.country,
    required this.city,
    required this.salary,
    this.type,
    required this.logo,
    required this.countryFlag,
  });
}

class FeaturedJobCard extends StatelessWidget {
  final Job job;

  const FeaturedJobCard({Key? key, required this.job}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        NavigationService().navigateToScreen('job_details');
      },
      child: Container(
        width: 320,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(DesignConstants.radiusLarge),
          border: Border(
            left: BorderSide(
              color: DesignConstants.primaryBlue,
              width: 4,
            ),
          ),
          boxShadow: DesignConstants.shadowMedium,
        ),
        child: Container(
          decoration: BoxDecoration(
            color: DesignConstants.secondaryBlue.withOpacity(0.05),
            borderRadius: BorderRadius.circular(DesignConstants.radiusLarge),
          ),
          padding: EdgeInsets.all(DesignConstants.spaceL),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              // Prvi red: naslov + kompanija | ikonica kompanije
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          job.title,
                          style: DesignConstants.titleMedium.copyWith(
                            color: DesignConstants.textPrimary,
                            height: 1.2,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                        SizedBox(height: DesignConstants.spaceXS),
                        Text(
                          job.company,
                          style: DesignConstants.labelMedium.copyWith(
                            color: DesignConstants.textSecondary,
                          ),
                        ),
                      ],
                    ),
                  ),
                  SizedBox(width: DesignConstants.spaceS),
                  Container(
                    width: 44,
                    height: 44,
                    decoration: BoxDecoration(
                      color: DesignConstants.primaryBlue.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(DesignConstants.radiusMedium),
                    ),
                    child: Icon(
                      Icons.business,
                      color: DesignConstants.primaryBlue,
                      size: DesignConstants.iconMedium,
                    ),
                  ),
                ],
              ),
              SizedBox(height: DesignConstants.spaceS),
              // Drugi red: lokacija info | zastava
              Row(
                children: [
                  Expanded(
                    child: Row(
                      children: [
                        Row(
                          children: [
                            Icon(
                              Icons.public,
                              size: DesignConstants.iconSmall,
                              color: DesignConstants.textSecondary,
                            ),
                            SizedBox(width: DesignConstants.spaceXS),
                            Text(
                              job.country,
                              style: DesignConstants.labelMedium.copyWith(
                                color: DesignConstants.textPrimary,
                              ),
                            ),
                          ],
                        ),
                        SizedBox(width: DesignConstants.spaceM),
                        Row(
                          children: [
                            Icon(
                              Icons.location_on_outlined,
                              size: DesignConstants.iconSmall,
                              color: DesignConstants.textSecondary,
                            ),
                            SizedBox(width: DesignConstants.spaceXS),
                            Text(
                              job.city,
                              style: DesignConstants.labelMedium.copyWith(
                                color: DesignConstants.textSecondary,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                  SizedBox(width: DesignConstants.spaceM),
                  Container(
                    width: 48,
                    height: 48,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(24),
                      border: Border.all(
                        color: DesignConstants.borderGray.withOpacity(0.5),
                        width: 1,
                      ),
                    ),
                    child: Center(
                      child: Text(
                        job.countryFlag,
                        style: TextStyle(fontSize: 24),
                      ),
                    ),
                  ),
                ],
              ),
              SizedBox(height: DesignConstants.spaceS),
              // Plata u donjem lijevom uglu
              Align(
                alignment: Alignment.centerLeft,
                child: Container(
                  padding: EdgeInsets.symmetric(
                    horizontal: DesignConstants.spaceM,
                    vertical: DesignConstants.spaceS,
                  ),
                  decoration: BoxDecoration(
                    color: DesignConstants.successGreen.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(DesignConstants.radiusSmall),
                  ),
                  child: Text(
                    job.salary,
                    style: DesignConstants.labelMedium.copyWith(
                      color: DesignConstants.successGreen,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class RegularJobCard extends StatelessWidget {
  final Job job;

  const RegularJobCard({Key? key, required this.job}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        NavigationService().navigateToScreen('job_details');
      },
      child: Container(
        margin: EdgeInsets.only(bottom: DesignConstants.spaceL),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              DesignConstants.white,
              DesignConstants.modernPrimaryBlue.withOpacity(0.01),
            ],
          ),
          borderRadius: BorderRadius.circular(DesignConstants.radiusXLarge),
          border: Border.all(
            color: DesignConstants.modernPrimaryBlue.withOpacity(0.1),
            width: 1.5,
          ),
          boxShadow: [
            BoxShadow(
              color: DesignConstants.modernPrimaryBlue.withOpacity(0.05),
              blurRadius: 12,
              offset: Offset(0, 4),
              spreadRadius: 1,
            ),
            BoxShadow(
              color: Colors.black.withOpacity(0.03),
              blurRadius: 8,
              offset: Offset(0, 2),
            ),
          ],
        ),
        child: Padding(
          padding: EdgeInsets.all(DesignConstants.spaceXL),
          child: Row(
            children: [
              Column(
                children: [
                  Container(
                    width: 44,
                    height: 44,
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                        colors: [
                          DesignConstants.modernPrimaryBlue.withOpacity(0.1),
                          DesignConstants.modernPrimaryBlue.withOpacity(0.05),
                        ],
                      ),
                      borderRadius: BorderRadius.circular(DesignConstants.radiusLarge),
                      border: Border.all(
                        color: DesignConstants.modernPrimaryBlue.withOpacity(0.15),
                        width: 1,
                      ),
                    ),
                    child: Icon(
                      Icons.business_rounded,
                      color: DesignConstants.modernPrimaryBlue,
                      size: 22,
                    ),
                  ),
                  SizedBox(height: DesignConstants.spaceS),
                  Container(
                    width: 44,
                    height: 44,
                    decoration: BoxDecoration(
                      color: DesignConstants.modernPrimaryBlue.withOpacity(0.05),
                      borderRadius: BorderRadius.circular(22),
                      border: Border.all(
                        color: DesignConstants.modernPrimaryBlue.withOpacity(0.15),
                        width: 1,
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: DesignConstants.modernPrimaryBlue.withOpacity(0.08),
                          blurRadius: 6,
                          offset: Offset(0, 2),
                        ),
                      ],
                    ),
                    child: Center(
                      child: Text(
                        job.countryFlag,
                        style: TextStyle(fontSize: 22),
                      ),
                    ),
                  ),
                ],
              ),
              SizedBox(width: DesignConstants.spaceL),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      job.title,
                      style: DesignConstants.titleSmall.copyWith(
                        letterSpacing: -0.2,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    SizedBox(height: DesignConstants.spaceXS),
                    Text(
                      job.company,
                      style: DesignConstants.labelMedium.copyWith(
                        color: DesignConstants.modernPrimaryBlue,
                      ),
                    ),
                    SizedBox(height: DesignConstants.spaceS),
                    Row(
                      children: [
                        Icon(
                          Icons.public,
                          size: DesignConstants.iconSmall,
                          color: DesignConstants.textSecondary,
                        ),
                        SizedBox(width: DesignConstants.spaceXS),
                        Expanded(
                          child: Text(
                            job.type != null 
                              ? '${job.country} • ${job.city} • ${job.type}'
                              : '${job.country} • ${job.city}',
                            style: DesignConstants.bodySmall.copyWith(
                              color: DesignConstants.darkGray,
                            ),
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: DesignConstants.spaceXS),
                    Container(
                      padding: EdgeInsets.symmetric(
                        horizontal: DesignConstants.spaceM,
                        vertical: DesignConstants.spaceS,
                      ),
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [
                            DesignConstants.modernSuccessGreen.withOpacity(0.12),
                            DesignConstants.modernSuccessGreen.withOpacity(0.06),
                          ],
                        ),
                        borderRadius: BorderRadius.circular(DesignConstants.radiusMedium),
                        border: Border.all(
                          color: DesignConstants.modernSuccessGreen.withOpacity(0.2),
                          width: 1,
                        ),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            Icons.euro_rounded,
                            color: DesignConstants.modernSuccessGreen,
                            size: 14,
                          ),
                          SizedBox(width: DesignConstants.spaceXS),
                          Text(
                            job.salary,
                            style: DesignConstants.labelSmall.copyWith(
                              fontWeight: FontWeight.w700,
                              color: DesignConstants.modernSuccessGreen,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
              Container(
                padding: EdgeInsets.all(DesignConstants.spaceS),
                decoration: BoxDecoration(
                  color: DesignConstants.modernPrimaryBlue.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(DesignConstants.radiusMedium),
                ),
                child: Icon(
                  Icons.arrow_forward_ios_rounded,
                  size: DesignConstants.iconSmall,
                  color: DesignConstants.modernPrimaryBlue,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}