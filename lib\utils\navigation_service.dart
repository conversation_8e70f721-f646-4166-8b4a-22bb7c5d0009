import 'package:flutter/material.dart';

class NavigationService {
  static final NavigationService _instance = NavigationService._internal();
  factory NavigationService() => _instance;
  NavigationService._internal();

  Function(String, {Map<String, dynamic>? data})? _navigateToScreen;
  Function()? _navigateBack;

  void setNavigationCallbacks({
    required Function(String, {Map<String, dynamic>? data}) navigateToScreen,
    required Function() navigateBack,
  }) {
    _navigateToScreen = navigateToScreen;
    _navigateBack = navigateBack;
  }

  void navigateToScreen(String screenName, {Map<String, dynamic>? data}) {
    _navigateToScreen?.call(screenName, data: data);
  }

  void navigateBack() {
    _navigateBack?.call();
  }
}