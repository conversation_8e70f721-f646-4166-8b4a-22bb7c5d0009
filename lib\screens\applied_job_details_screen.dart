import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../utils/navigation_service.dart';
import '../constants/design_constants.dart';

class AppliedJobDetailsScreen extends StatefulWidget {
  final Map<String, dynamic> job;
  final Map<String, dynamic> application;

  const AppliedJobDetailsScreen({
    Key? key,
    required this.job,
    required this.application,
  }) : super(key: key);

  @override
  _AppliedJobDetailsScreenState createState() => _AppliedJobDetailsScreenState();
}

class _AppliedJobDetailsScreenState extends State<AppliedJobDetailsScreen> {
  @override
  Widget build(BuildContext context) {
    return Material(
      color: const Color(0xFFFAFBFC),
      child: Stack(
        children: [
          Column(
            children: [
              _buildApplicationStatusBar(),
              Expanded(
                child: SingleChildScrollView(
                  padding: EdgeInsets.only(bottom: 100),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildJobHeaderCard(),
                      _buildJobDescription(),
                      _buildRequirementsSection(),
                      _buildBenefitsSection(),
                      SizedBox(height: 80), // Space for floating button
                    ],
                  ),
                ),
              ),
            ],
          ),
          // Floating Apply Button
          Positioned(
            left: 20,
            right: 20,
            bottom: 20,
            child: SafeArea(
              child: _buildFloatingApplyButton(),
            ),
          ),
        ],
      ),
    );
  }


  Widget _buildApplicationStatusBar() {
    return Container(
      color: const Color(0xFF059669).withOpacity(0.1),
      child: Container(
        width: double.infinity,
        padding: EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: const Color(0xFF059669).withOpacity(0.1),
          border: Border(
            bottom: BorderSide(
              color: const Color(0xFF059669).withOpacity(0.3),
              width: 1,
            ),
          ),
        ),
        child: Row(
          children: [
            Container(
              padding: EdgeInsets.all(6),
              decoration: BoxDecoration(
                color: const Color(0xFF059669),
                shape: BoxShape.circle,
              ),
              child: Icon(
                Icons.check,
                color: Colors.white,
                size: 16,
              ),
            ),
            SizedBox(width: 12),
            Expanded(
              child: Text(
                'Prijavili ste se za ovaj posao',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: const Color(0xFF059669),
                ),
              ),
            ),
            Text(
              widget.application['applicationDate'] ?? 'Prije 3 dana',
              style: TextStyle(
                fontSize: 12,
                color: const Color(0xFF059669).withOpacity(0.8),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildJobHeaderCard() {
    return Container(
      margin: EdgeInsets.fromLTRB(DesignConstants.spaceL, DesignConstants.spaceM, DesignConstants.spaceL, 0),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            DesignConstants.white,
            DesignConstants.modernPrimaryBlue.withOpacity(0.02),
          ],
        ),
        borderRadius: BorderRadius.circular(DesignConstants.radiusXLarge),
        border: Border.all(
          color: DesignConstants.modernPrimaryBlue.withOpacity(0.1),
          width: 1.5,
        ),
        boxShadow: [
          BoxShadow(
            color: DesignConstants.modernPrimaryBlue.withOpacity(0.05),
            blurRadius: 12,
            offset: Offset(0, 4),
            spreadRadius: 1,
          ),
          BoxShadow(
            color: Colors.black.withOpacity(0.03),
            blurRadius: 8,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: EdgeInsets.all(DesignConstants.spaceXL),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        widget.job['title'] ?? 'Frontend Developer',
                        style: DesignConstants.titleLarge.copyWith(
                          color: DesignConstants.black,
                          height: 1.2,
                        ),
                      ),
                      SizedBox(height: DesignConstants.spaceXS),
                      Text(
                        widget.job['company'] ?? 'WebSolutions',
                        style: DesignConstants.titleSmall.copyWith(
                          color: DesignConstants.modernPrimaryBlue,
                        ),
                      ),
                      SizedBox(height: DesignConstants.spaceS),
                      Row(
                        children: [
                          Text(
                            widget.job['countryFlag'] ?? '🇩🇪',
                            style: TextStyle(fontSize: 18),
                          ),
                          SizedBox(width: DesignConstants.spaceXS),
                          Text(
                            '${widget.job['city'] ?? 'Hamburg'}, ${widget.job['country'] ?? 'Njemačka'}',
                            style: DesignConstants.bodyMedium.copyWith(
                              color: DesignConstants.darkGray,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                SizedBox(width: 12),
                Text(
                  widget.job['logo'] ?? '🏢',
                  style: TextStyle(fontSize: 48),
                ),
              ],
            ),
            SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  widget.job['salary'] ?? '€3,000 - €4,200',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: const Color(0xFF1E3A8A),
                  ),
                ),
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: const Color(0xFFF1F5F9),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    widget.job['type'] ?? 'Puno radno vrijeme',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      color: const Color(0xFF475569),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildJobDescription() {
    return Container(
      margin: EdgeInsets.fromLTRB(DesignConstants.spaceL, DesignConstants.spaceXL, DesignConstants.spaceL, 0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Opis posla',
            style: DesignConstants.titleMedium.copyWith(
              color: DesignConstants.black,
            ),
          ),
          SizedBox(height: DesignConstants.spaceM),
          Container(
            width: double.infinity,
            decoration: DesignConstants.modernGlassCard,
            padding: EdgeInsets.all(DesignConstants.spaceXL),
            child: Text(
              widget.job['description'] ?? 'Pridružite se našem dinamičnom timu kao Frontend Developer i radite na inovativnim web projektima. Tražimo kreativnu osobu koja će pomoći u razvoju modernih korisničkih interfejsa koristeći najnovije tehnologije. Ova pozicija nudi odličnu priliku za profesionalni rast u međunarodnom okruženju.',
              style: DesignConstants.bodyMedium.copyWith(
                height: 1.6,
                color: DesignConstants.darkGray,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRequirementsSection() {
    final requirements = widget.job['requirements'] ?? [
      'Njemački jezik - nivo B2 ili viši',
      'Vozačka dozvola kategorije B',
      'Minimum 2 godine iskustva u React.js',
      'Poznavanje TypeScript-a',
      'Iskustvo sa Tailwind CSS',
      'Razumijevanje Git workflow-a',
      'Komunikacijske vještine'
    ];
    
    return Container(
      margin: EdgeInsets.fromLTRB(DesignConstants.spaceL, DesignConstants.spaceXL, DesignConstants.spaceL, 0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Zahtjevi',
            style: DesignConstants.titleMedium.copyWith(
              color: DesignConstants.black,
            ),
          ),
          SizedBox(height: DesignConstants.spaceM),
          Container(
            width: double.infinity,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: const Color(0xFFE2E8F0),
                width: 1,
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.05),
                  blurRadius: 4,
                  offset: Offset(0, 1),
                ),
              ],
            ),
            padding: EdgeInsets.all(DesignConstants.spaceXL),
            child: Column(
              children: requirements.asMap().entries.map<Widget>((entry) {
                int index = entry.key;
                String requirement = entry.value;
                IconData icon = _getRequirementIcon(index);
                
                return Padding(
                  padding: EdgeInsets.only(bottom: index < requirements.length - 1 ? 12 : 0),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Container(
                        margin: EdgeInsets.only(top: 2),
                        child: Icon(
                          icon,
                          size: 16,
                          color: const Color(0xFF1E3A8A),
                        ),
                      ),
                      SizedBox(width: 12),
                      Expanded(
                        child: Text(
                          requirement,
                          style: TextStyle(
                            fontSize: 14,
                            color: const Color(0xFF0A0A0A),
                          ),
                        ),
                      ),
                    ],
                  ),
                );
              }).toList(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBenefitsSection() {
    final benefits = widget.job['benefits'] ?? [
      'Fleksibilno radno vrijeme',
      'Mogućnost rada od kuće',
      'Privatno zdravstveno osiguranje',
      'Godišnji bonus',
      'Besplatna kafa i grickalice',
      'Tim building eventi',
      'Mogućnost napredovanja',
      'Moderni radni prostor'
    ];
    
    return Container(
      margin: EdgeInsets.fromLTRB(DesignConstants.spaceL, DesignConstants.spaceXL, DesignConstants.spaceL, 0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Benefiti',
            style: DesignConstants.titleMedium.copyWith(
              color: DesignConstants.black,
            ),
          ),
          SizedBox(height: DesignConstants.spaceM),
          Container(
            width: double.infinity,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: const Color(0xFFE2E8F0),
                width: 1,
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.05),
                  blurRadius: 4,
                  offset: Offset(0, 1),
                ),
              ],
            ),
            padding: EdgeInsets.all(DesignConstants.spaceXL),
            child: Column(
              children: benefits.asMap().entries.map<Widget>((entry) {
                int index = entry.key;
                String benefit = entry.value;
                IconData icon = _getBenefitIcon(index);
                
                return Padding(
                  padding: EdgeInsets.only(bottom: index < benefits.length - 1 ? 12 : 0),
                  child: Row(
                    children: [
                      Container(
                        margin: EdgeInsets.only(top: 2),
                        child: Icon(
                          icon,
                          size: 16,
                          color: const Color(0xFF059669),
                        ),
                      ),
                      SizedBox(width: 12),
                      Expanded(
                        child: Text(
                          benefit,
                          style: TextStyle(
                            fontSize: 14,
                            color: const Color(0xFF0A0A0A),
                          ),
                        ),
                      ),
                    ],
                  ),
                );
              }).toList(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFloatingApplyButton() {
    return Container(
      decoration: BoxDecoration(
        boxShadow: [
          BoxShadow(
            color: const Color(0xFF059669).withOpacity(0.3),
            blurRadius: 20,
            offset: Offset(0, 8),
          ),
        ],
      ),
      child: ElevatedButton(
        onPressed: () {
          NavigationService().navigateToScreen('application_details', data: {
            'application': widget.application,
          });
        },
        style: ElevatedButton.styleFrom(
          backgroundColor: const Color(0xFF059669),
          foregroundColor: Colors.white,
          elevation: 0,
          shadowColor: Colors.transparent,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          padding: EdgeInsets.symmetric(vertical: 16),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.visibility_outlined,
              size: 20,
            ),
            SizedBox(width: 8),
            Text(
              'Pogledajte Vašu prijavu',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      ),
    );
  }

  IconData _getRequirementIcon(int index) {
    switch (index) {
      case 0:
        return Icons.language;
      case 1:
        return Icons.directions_car;
      default:
        return Icons.check_circle_outline;
    }
  }

  IconData _getBenefitIcon(int index) {
    switch (index) {
      case 0:
        return Icons.access_time;
      case 1:
        return Icons.business;
      case 2:
        return Icons.security;
      case 3:
        return Icons.euro;
      case 4:
        return Icons.coffee;
      case 5:
        return Icons.people;
      case 6:
        return Icons.trending_up;
      case 7:
        return Icons.favorite;
      default:
        return Icons.check_circle_outline;
    }
  }
}
