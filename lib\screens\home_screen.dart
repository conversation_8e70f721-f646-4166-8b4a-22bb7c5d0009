import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'ssc_services_screen.dart';
import '../utils/navigation_service.dart';
import '../constants/design_constants.dart';

class HomeScreen extends StatelessWidget {
  double _calculateProfileCompletion() {
    int completedFields = 7;
    int totalFields = 15;
    return (completedFields / totalFields * 100);
  }

  @override
  Widget build(BuildContext context) {
    final profileCompletion = _calculateProfileCompletion();
    final screenWidth = MediaQuery.of(context).size.width;
    
    return Scaffold(
      backgroundColor: DesignConstants.backgroundLight,
      body: SingleChildScrollView(
        padding: EdgeInsets.all(DesignConstants.spaceXL),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Profile Completion - Most Important
            Container(
              width: double.infinity,
              padding: EdgeInsets.all(DesignConstants.spaceXL),
              decoration: BoxDecoration(
                color: DesignConstants.white,
                borderRadius: BorderRadius.circular(DesignConstants.radiusXLarge),
                border: Border.all(
                  color: DesignConstants.lightGray,
                  width: 1,
                ),
                boxShadow: DesignConstants.shadowMedium,
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Container(
                        padding: EdgeInsets.all(DesignConstants.spaceS),
                        decoration: BoxDecoration(
                          color: DesignConstants.modernPrimaryBlue.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(DesignConstants.radiusMedium),
                        ),
                        child: Icon(
                          Icons.person_outline_rounded,
                          color: DesignConstants.modernPrimaryBlue,
                          size: DesignConstants.iconMedium,
                        ),
                      ),
                      SizedBox(width: 12),
                      Expanded(
                        child: Text(
                          'Dopunite vaš profil',
                          style: DesignConstants.titleMedium.copyWith(
                            color: DesignConstants.black,
                          ),
                        ),
                      ),
                      Container(
                        padding: EdgeInsets.symmetric(
                          horizontal: DesignConstants.spaceM,
                          vertical: DesignConstants.spaceXS,
                        ),
                        decoration: BoxDecoration(
                          color: DesignConstants.modernPrimaryBlue.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(DesignConstants.radiusMedium),
                        ),
                        child: Text(
                          '${profileCompletion.toInt()}%',
                          style: DesignConstants.labelLarge.copyWith(
                            color: DesignConstants.modernPrimaryBlue,
                          ),
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 16),
                  Container(
                    height: 10,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(DesignConstants.spaceXS),
                      color: DesignConstants.lightGray,
                    ),
                    child: LinearProgressIndicator(
                      value: profileCompletion / 100,
                      backgroundColor: Colors.transparent,
                      valueColor: AlwaysStoppedAnimation<Color>(DesignConstants.modernPrimaryBlue),
                      minHeight: 10,
                    ),
                  ),
                  SizedBox(height: 12),
                  Text(
                    'Kandidati sa kompletnim profilom dobijaju 5x više poziva',
                    style: DesignConstants.bodySmall.copyWith(
                      color: DesignConstants.darkGray,
                    ),
                  ),
                  SizedBox(height: 16),
                  Container(
                    width: double.infinity,
                    height: 52,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(DesignConstants.radiusLarge),
                      boxShadow: DesignConstants.modernBrandButtonShadow,
                    ),
                    child: ElevatedButton(
                      onPressed: () {
                        // Navigate to profile
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: DesignConstants.modernPrimaryBlue,
                        foregroundColor: DesignConstants.white,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(DesignConstants.radiusLarge),
                        ),
                        elevation: 0,
                        shadowColor: Colors.transparent,
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(
                            'Dopuni profil',
                            style: DesignConstants.buttonLarge,
                          ),
                          SizedBox(width: DesignConstants.spaceS),
                          Icon(
                            Icons.arrow_forward_rounded,
                            size: DesignConstants.iconMedium,
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),

            SizedBox(height: DesignConstants.spaceXL),

            // Quick Stats - Simple 2-column
            Row(
              children: [
                Expanded(child: _buildStatCard('Vaše prijave', '3', DesignConstants.modernSuccessGreen)),
                SizedBox(width: DesignConstants.spaceM),
                Expanded(child: _buildStatCard('Pozivnice', '1', DesignConstants.modernWarningYellow)),
              ],
            ),

            SizedBox(height: DesignConstants.spaceXL),

            // EU Jobs Card
            Container(
              width: double.infinity,
              padding: EdgeInsets.all(DesignConstants.spaceXL),
              decoration: BoxDecoration(
                color: DesignConstants.white,
                borderRadius: BorderRadius.circular(DesignConstants.radiusXLarge),
                border: Border.all(
                  color: DesignConstants.lightGray,
                  width: 1,
                ),
                boxShadow: DesignConstants.shadowMedium,
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Container(
                        padding: EdgeInsets.all(DesignConstants.spaceS),
                        decoration: BoxDecoration(
                          color: DesignConstants.modernPrimaryBlue.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(DesignConstants.radiusMedium),
                        ),
                        child: Icon(
                          Icons.public_outlined,
                          color: DesignConstants.modernPrimaryBlue,
                          size: DesignConstants.iconMedium,
                        ),
                      ),
                      SizedBox(width: 12),
                      Expanded(
                        child: Text(
                          'Najnoviji poslovi u EU',
                          style: DesignConstants.titleMedium.copyWith(
                            color: DesignConstants.black,
                          ),
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 16),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: [
                      _buildCountryFlag('🇩🇪', 'Njemačka'),
                      _buildCountryFlag('🇦🇹', 'Austrija'),
                      _buildCountryFlag('🇨🇭', 'Švicarska'),
                      _buildCountryFlag('🇭🇷', 'Hrvatska'),
                      _buildCountryFlag('🇸🇮', 'Slovenija'),
                    ],
                  ),
                  SizedBox(height: 20),
                  Container(
                    width: double.infinity,
                    height: 52,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(DesignConstants.radiusLarge),
                      boxShadow: DesignConstants.modernBrandButtonShadow,
                    ),
                    child: ElevatedButton(
                      onPressed: () {
                        // Navigate to jobs
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: DesignConstants.modernPrimaryBlue,
                        foregroundColor: DesignConstants.white,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(DesignConstants.radiusLarge),
                        ),
                        elevation: 0,
                        shadowColor: Colors.transparent,
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(
                            'Pogledaj poslove',
                            style: DesignConstants.buttonLarge,
                          ),
                          SizedBox(width: DesignConstants.spaceS),
                          Icon(
                            Icons.work_rounded,
                            size: DesignConstants.iconMedium,
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),

            SizedBox(height: DesignConstants.spaceXL),

            // Visa Help Card
            Container(
              width: double.infinity,
              padding: EdgeInsets.all(DesignConstants.spaceXL),
              decoration: BoxDecoration(
                color: DesignConstants.white,
                borderRadius: BorderRadius.circular(DesignConstants.radiusXLarge),
                border: Border.all(
                  color: DesignConstants.lightGray,
                  width: 1,
                ),
                boxShadow: DesignConstants.shadowMedium,
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Container(
                        padding: EdgeInsets.all(DesignConstants.spaceS),
                        decoration: BoxDecoration(
                          color: DesignConstants.modernPrimaryBlue.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(DesignConstants.radiusMedium),
                        ),
                        child: Icon(
                          Icons.description_outlined,
                          color: DesignConstants.modernPrimaryBlue,
                          size: DesignConstants.iconMedium,
                        ),
                      ),
                      SizedBox(width: 12),
                      Expanded(
                        child: Text(
                          'Pomoć oko dokumentacije i vize',
                          style: DesignConstants.titleMedium.copyWith(
                            color: DesignConstants.black,
                          ),
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 12),
                  Text(
                    'Pronašli ste posao i treba Vam pomoć oko vize?',
                    style: DesignConstants.bodySmall.copyWith(
                      color: DesignConstants.darkGray,
                    ),
                  ),
                  SizedBox(height: 16),
                  Container(
                    width: double.infinity,
                    height: 52,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(DesignConstants.radiusLarge),
                      boxShadow: DesignConstants.modernBrandButtonShadow,
                    ),
                    child: ElevatedButton(
                      onPressed: () {
                        NavigationService().navigateToScreen('ssc_services');
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: DesignConstants.modernPrimaryBlue,
                        foregroundColor: DesignConstants.white,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(DesignConstants.radiusLarge),
                        ),
                        elevation: 0,
                        shadowColor: Colors.transparent,
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(
                            'Vidi mogućnosti',
                            style: DesignConstants.buttonLarge,
                          ),
                          SizedBox(width: DesignConstants.spaceS),
                          Icon(
                            Icons.description_rounded,
                            size: DesignConstants.iconMedium,
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),

            SizedBox(height: DesignConstants.spaceMassive + DesignConstants.spaceXL),
          ],
        ),
      ),
    );
  }

  Widget _buildStatCard(String title, String count, Color accentColor) {
    return Container(
      padding: EdgeInsets.all(DesignConstants.spaceL),
      decoration: BoxDecoration(
        color: DesignConstants.white,
        borderRadius: BorderRadius.circular(DesignConstants.radiusLarge),
        border: Border.all(
          color: DesignConstants.lightGray,
          width: 1,
        ),
        boxShadow: DesignConstants.shadowSmall,
      ),
      child: Column(
        children: [
          Container(
            padding: EdgeInsets.all(DesignConstants.spaceS),
            decoration: BoxDecoration(
              color: accentColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(DesignConstants.radiusMedium),
            ),
            child: Text(
              count,
              style: DesignConstants.headlineSmall.copyWith(
                color: accentColor,
              ),
            ),
          ),
          SizedBox(height: DesignConstants.spaceS),
          Text(
            title,
            textAlign: TextAlign.center,
            style: DesignConstants.labelSmall.copyWith(
              color: DesignConstants.darkGray,
            ),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  Widget _buildCountryFlag(String flag, String country) {
    return Column(
      children: [
        Container(
          width: 56,
          height: 56,
          decoration: BoxDecoration(
            color: DesignConstants.modernPrimaryBlue.withOpacity(0.05),
            borderRadius: BorderRadius.circular(DesignConstants.radiusHuge),
            border: Border.all(
              color: DesignConstants.modernPrimaryBlue.withOpacity(0.1),
              width: 1,
            ),
            boxShadow: DesignConstants.shadowSmall,
          ),
          child: Center(
            child: Text(
              flag,
              style: TextStyle(fontSize: 28),
            ),
          ),
        ),
        SizedBox(height: DesignConstants.spaceS),
        Text(
          country,
          style: DesignConstants.labelSmall.copyWith(
            fontSize: 11,
            color: DesignConstants.darkGray,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }
}