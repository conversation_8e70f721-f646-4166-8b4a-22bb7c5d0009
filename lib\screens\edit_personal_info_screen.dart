import 'package:flutter/material.dart';
import '../constants/design_constants.dart';

class EditPersonalInfoScreen extends StatefulWidget {
  @override
  _EditPersonalInfoScreenState createState() => _EditPersonalInfoScreenState();
}

class _EditPersonalInfoScreenState extends State<EditPersonalInfoScreen> {
  final _formKey = GlobalKey<FormState>();
  
  // Controllers for text fields
  late TextEditingController _firstNameController;
  late TextEditingController _lastNameController;
  late TextEditingController _phoneController;
  
  bool _hasChanges = false;
  
  @override
  void initState() {
    super.initState();
    
    // Initialize controllers with current data
    _firstNameController = TextEditingController(text: 'Stefan');
    _lastNameController = TextEditingController(text: '<PERSON><PERSON><PERSON>');
    _phoneController = TextEditingController(text: '+381 62 987 654');
    
    // Add listeners to detect changes
    _firstNameController.addListener(_onFieldChanged);
    _lastNameController.addListener(_onFieldChanged);
    _phoneController.addListener(_onFieldChanged);
  }
  
  @override
  void dispose() {
    _firstNameController.dispose();
    _lastNameController.dispose();
    _phoneController.dispose();
    super.dispose();
  }
  
  void _onFieldChanged() {
    if (!_hasChanges) {
      setState(() {
        _hasChanges = true;
      });
    }
  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.center,
            colors: [
              DesignConstants.modernPrimaryBlue,
              DesignConstants.modernPrimaryBlue.withOpacity(0.1),
            ],
            stops: [0.0, 0.3],
          ),
        ),
        child: SafeArea(
          child: Stack(
            children: [
              Column(
                children: [
                  // Custom AppBar with gradient
                  Container(
                    padding: EdgeInsets.symmetric(horizontal: 20, vertical: 16),
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.15),
                      border: Border(
                        bottom: BorderSide(
                          color: Colors.white.withOpacity(0.2),
                          width: 1,
                        ),
                      ),
                    ),
                    child: Row(
                      children: [
                        Container(
                          decoration: BoxDecoration(
                            color: Colors.white.withOpacity(0.2),
                            borderRadius: BorderRadius.circular(DesignConstants.radiusMedium),
                            border: Border.all(
                              color: Colors.white.withOpacity(0.3),
                              width: 1,
                            ),
                          ),
                          child: IconButton(
                            onPressed: () => Navigator.pop(context),
                            icon: Icon(
                              Icons.arrow_back_ios,
                              color: DesignConstants.white,
                              size: DesignConstants.iconMedium,
                            ),
                            padding: EdgeInsets.all(8),
                          ),
                        ),
                        Expanded(
                          child: Text(
                            'Uredi osobne podatke',
                            textAlign: TextAlign.center,
                            style: DesignConstants.titleMedium.copyWith(
                              color: DesignConstants.white,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                        // Invisible container for balance
                        Container(width: 48, height: 48),
                      ],
                    ),
                  ),
                  // Content area
                  Expanded(
                    child: Container(
                      width: double.infinity,
                      decoration: BoxDecoration(
                        color: DesignConstants.surfaceGray,
                        borderRadius: BorderRadius.only(
                          topLeft: Radius.circular(DesignConstants.radiusXLarge),
                          topRight: Radius.circular(DesignConstants.radiusXLarge),
                        ),
                      ),
                      child: SingleChildScrollView(
                        padding: EdgeInsets.fromLTRB(20, 20, 20, 100), // Bottom padding for floating button
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            _buildModernHeader(),
                            SizedBox(height: DesignConstants.spaceXXL),
                            _buildModernFormCard(),
                          ],
                        ),
                      ),
                    ),
                  ),
                ],
              ),
              
              // Floating Save Button
              if (_hasChanges)
                Positioned(
                  left: 20,
                  right: 20,
                  bottom: 20,
                  child: _buildFloatingSaveButton(),
                ),
            ],
          ),
        ),
      ),
    );
  }
  
  
  Widget _buildModernHeader() {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(DesignConstants.spaceXXL),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            DesignConstants.white,
            DesignConstants.modernPrimaryBlue.withOpacity(0.02),
          ],
        ),
        borderRadius: BorderRadius.circular(DesignConstants.radiusXXLarge),
        border: Border.all(
          color: DesignConstants.modernPrimaryBlue.withOpacity(0.15),
          width: 1.5,
        ),
        boxShadow: [
          BoxShadow(
            color: DesignConstants.modernPrimaryBlue.withOpacity(0.08),
            blurRadius: 20,
            offset: Offset(0, 8),
            spreadRadius: 1,
          ),
          BoxShadow(
            color: Colors.black.withOpacity(0.03),
            blurRadius: 8,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: EdgeInsets.all(DesignConstants.spaceM),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      DesignConstants.modernPrimaryBlue.withOpacity(0.1),
                      DesignConstants.modernPrimaryBlue.withOpacity(0.05),
                    ],
                  ),
                  borderRadius: BorderRadius.circular(DesignConstants.radiusLarge),
                ),
                child: Icon(
                  Icons.person_outline,
                  color: DesignConstants.modernPrimaryBlue,
                  size: DesignConstants.iconLarge,
                ),
              ),
              SizedBox(width: DesignConstants.spaceL),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Osobni podaci',
                      style: DesignConstants.titleMedium.copyWith(
                        color: DesignConstants.black,
                      ),
                    ),
                    SizedBox(height: DesignConstants.spaceXS),
                    Text(
                      'Uredite svoje osnovne informacije i kontakt podatke',
                      style: DesignConstants.bodyMedium.copyWith(
                        color: DesignConstants.darkGray,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildModernFormCard() {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(DesignConstants.spaceXXL),
      decoration: BoxDecoration(
        color: DesignConstants.white,
        borderRadius: BorderRadius.circular(DesignConstants.radiusXLarge),
        border: Border.all(
          color: DesignConstants.lightGray.withOpacity(0.5),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.04),
            blurRadius: 16,
            offset: Offset(0, 4),
          ),
          BoxShadow(
            color: Colors.black.withOpacity(0.02),
            blurRadius: 4,
            offset: Offset(0, 1),
          ),
        ],
      ),
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildModernTextField(
              label: 'Ime',
              controller: _firstNameController,
              icon: Icons.person_outline,
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Ime je obavezno';
                }
                return null;
              },
            ),
            SizedBox(height: DesignConstants.spaceXXL),
            _buildModernTextField(
              label: 'Prezime',
              controller: _lastNameController,
              icon: Icons.badge_outlined,
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Prezime je obavezno';
                }
                return null;
              },
            ),
            SizedBox(height: DesignConstants.spaceXXL),
            _buildModernTextField(
              label: 'Broj telefona',
              controller: _phoneController,
              icon: Icons.phone_outlined,
              keyboardType: TextInputType.phone,
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Broj telefona je obavezan';
                }
                return null;
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFloatingSaveButton() {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        gradient: DesignConstants.modernPrimaryGradient,
        borderRadius: BorderRadius.circular(DesignConstants.radiusLarge),
        boxShadow: [
          BoxShadow(
            color: DesignConstants.modernPrimaryBlue.withOpacity(0.4),
            blurRadius: 20,
            offset: Offset(0, 8),
          ),
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: _saveChanges,
          borderRadius: BorderRadius.circular(DesignConstants.radiusLarge),
          child: Container(
            padding: EdgeInsets.symmetric(
              vertical: DesignConstants.spaceL + 2,
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.check_circle_outline,
                  color: DesignConstants.white,
                  size: DesignConstants.iconMedium,
                ),
                SizedBox(width: DesignConstants.spaceS),
                Text(
                  'Spremi promjene',
                  style: DesignConstants.buttonLarge.copyWith(
                    color: DesignConstants.white,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildModernTextField({
    required String label,
    required TextEditingController controller,
    required IconData icon,
    TextInputType? keyboardType,
    String? Function(String?)? validator,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Container(
              padding: EdgeInsets.all(6),
              decoration: BoxDecoration(
                color: DesignConstants.modernPrimaryBlue.withOpacity(0.1),
                borderRadius: BorderRadius.circular(6),
              ),
              child: Icon(
                icon,
                size: 16,
                color: DesignConstants.modernPrimaryBlue,
              ),
            ),
            SizedBox(width: DesignConstants.spaceS),
            Text(
              label,
              style: DesignConstants.labelMedium.copyWith(
                color: DesignConstants.black,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
        SizedBox(height: DesignConstants.spaceM),
        Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(DesignConstants.radiusMedium),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.02),
                blurRadius: 4,
                offset: Offset(0, 2),
              ),
            ],
          ),
          child: TextFormField(
            controller: controller,
            keyboardType: keyboardType,
            validator: validator,
            style: DesignConstants.bodyLarge.copyWith(
              color: DesignConstants.black,
            ),
            decoration: InputDecoration(
              filled: true,
              fillColor: DesignConstants.surfaceGray,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(DesignConstants.radiusMedium),
                borderSide: BorderSide(
                  color: DesignConstants.lightGray.withOpacity(0.3),
                  width: 1.5,
                ),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(DesignConstants.radiusMedium),
                borderSide: BorderSide(
                  color: DesignConstants.lightGray.withOpacity(0.3),
                  width: 1.5,
                ),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(DesignConstants.radiusMedium),
                borderSide: BorderSide(
                  color: DesignConstants.modernPrimaryBlue,
                  width: 2,
                ),
              ),
              errorBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(DesignConstants.radiusMedium),
                borderSide: BorderSide(
                  color: DesignConstants.modernErrorRed,
                  width: 1.5,
                ),
              ),
              focusedErrorBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(DesignConstants.radiusMedium),
                borderSide: BorderSide(
                  color: DesignConstants.modernErrorRed,
                  width: 2,
                ),
              ),
              contentPadding: EdgeInsets.symmetric(
                horizontal: DesignConstants.spaceL,
                vertical: DesignConstants.spaceM + 2,
              ),
              hintStyle: DesignConstants.bodyMedium.copyWith(
                color: DesignConstants.darkGray.withOpacity(0.6),
              ),
            ),
          ),
        ),
      ],
    );
  }

  void _saveChanges() {
    if (_formKey.currentState!.validate()) {
      // TODO: Save changes to backend/state management
      
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Container(
            padding: EdgeInsets.symmetric(vertical: 4),
            child: Row(
              children: [
                Container(
                  padding: EdgeInsets.all(6),
                  decoration: BoxDecoration(
                    color: DesignConstants.white.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Icon(
                    Icons.check_circle,
                    color: DesignConstants.white,
                    size: 16,
                  ),
                ),
                SizedBox(width: DesignConstants.spaceM),
                Expanded(
                  child: Text(
                    'Osobni podaci uspješno ažurirani!',
                    style: DesignConstants.labelMedium.copyWith(
                      color: DesignConstants.white,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
          ),
          backgroundColor: DesignConstants.modernSuccessGreen,
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(DesignConstants.radiusMedium),
          ),
          margin: EdgeInsets.all(DesignConstants.spaceL),
          elevation: 8,
          duration: Duration(seconds: 3),
        ),
      );
      
      Navigator.pop(context, true); // Return true to indicate changes were saved
    }
  }
}