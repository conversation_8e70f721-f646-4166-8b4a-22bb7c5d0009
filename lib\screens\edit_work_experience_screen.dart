import 'package:flutter/material.dart';
import '../constants/design_constants.dart';
import 'add_work_experience_screen.dart';

class EditWorkExperienceScreen extends StatefulWidget {
  @override
  _EditWorkExperienceScreenState createState() => _EditWorkExperienceScreenState();
}

class _EditWorkExperienceScreenState extends State<EditWorkExperienceScreen> {
  bool _hasChanges = false;
  
  // Mock current work experiences
  List<Map<String, dynamic>> _workExperiences = [
    {
      'position': 'Mašinski radnik',
      'company': 'Delta Holding d.o.o.',
      'location': 'Novi Sad',
      'startMonth': 8,
      'startYear': 2020,
      'endMonth': 3,
      'endYear': 2022,
      'isCurrentJob': false,
    },
    {
      'position': 'CNC operater',
      'company': 'Fiat Chrysler Automobiles',
      'location': 'Kragujevac',
      'startMonth': 4,
      'startYear': 2022,
      'endMonth': null,
      'endYear': null,
      'isCurrentJob': true,
    },
  ];
  
  final List<String> _months = [
    'Siječanj', '<PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', 'Travanj', '<PERSON><PERSON>banj', 'Lipanj',
    'Srpanj', 'Kolovoz', 'Rujan', 'Listopad', 'Studeni', 'Prosinac'
  ];
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: DesignConstants.modernPrimaryGradient,
        ),
        child: SafeArea(
          child: Stack(
            children: [
              Column(
                children: [
                  _buildModernAppBar(),
                  Expanded(
                    child: Container(
                      margin: EdgeInsets.only(top: 20),
                      decoration: BoxDecoration(
                        color: DesignConstants.backgroundGray,
                        borderRadius: BorderRadius.vertical(
                          top: Radius.circular(30),
                        ),
                      ),
                      child: SingleChildScrollView(
                        padding: EdgeInsets.fromLTRB(20, 30, 20, _hasChanges ? 100 : 20),
                        child: Column(
                          children: [
                            _buildModernHeader(),
                            SizedBox(height: 24),
                            _buildModernWorkExperiencesList(),
                            SizedBox(height: 20),
                            _buildModernAddExperienceButton(),
                          ],
                        ),
                      ),
                    ),
                  ),
                ],
              ),
              if (_hasChanges) _buildFloatingSaveButton(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildModernAppBar() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 20, vertical: 15),
      child: Row(
        children: [
          Container(
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.2),
              borderRadius: BorderRadius.circular(12),
            ),
            child: IconButton(
              onPressed: () => Navigator.pop(context),
              icon: Icon(
                Icons.arrow_back_ios_new,
                color: Colors.white,
                size: 20,
              ),
            ),
          ),
          Expanded(
            child: Center(
              child: Text(
                'Radno iskustvo',
                style: DesignConstants.titleMedium.copyWith(
                  color: Colors.white,
                ),
              ),
            ),
          ),
          SizedBox(width: 48),
        ],
      ),
    );
  }

  Widget _buildModernHeader() {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: DesignConstants.shadowMedium,
        border: Border.all(
          color: DesignConstants.borderGray,
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: EdgeInsets.all(16),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  DesignConstants.modernPrimaryBlue.withOpacity(0.1),
                  DesignConstants.modernPrimaryBlue.withOpacity(0.05),
                ],
              ),
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: DesignConstants.modernPrimaryBlue.withOpacity(0.2),
                width: 1,
              ),
            ),
            child: Icon(
              Icons.work_outline,
              color: DesignConstants.modernPrimaryBlue,
              size: 28,
            ),
          ),
          SizedBox(width: 20),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Vaša radna iskustva',
                  style: DesignConstants.titleSmall.copyWith(
                    color: DesignConstants.black,
                  ),
                ),
                SizedBox(height: 8),
                Text(
                  'Upravljajte vašim radnim iskustvima',
                  style: DesignConstants.bodyMedium.copyWith(
                    color: DesignConstants.darkGray,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildModernWorkExperiencesList() {
    return Column(
      children: _workExperiences.asMap().entries.map((entry) {
        int index = entry.key;
        Map<String, dynamic> experience = entry.value;
        
        return Container(
          margin: EdgeInsets.only(bottom: 16),
          child: _buildModernExperienceCard(experience, index),
        );
      }).toList(),
    );
  }

  Widget _buildModernExperienceCard(Map<String, dynamic> experience, int index) {
    String dateRange = _formatDateRange(experience);
    
    return Container(
      padding: EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: DesignConstants.shadowMedium,
        border: Border.all(
          color: DesignConstants.borderGray,
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      experience['position'],
                      style: DesignConstants.titleSmall.copyWith(
                        color: DesignConstants.black,
                      ),
                    ),
                    SizedBox(height: 8),
                    Text(
                      experience['company'],
                      style: DesignConstants.bodyLarge.copyWith(
                        color: DesignConstants.modernPrimaryBlue,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    SizedBox(height: 4),
                    Row(
                      children: [
                        Icon(
                          Icons.location_on_outlined,
                          size: 16,
                          color: DesignConstants.darkGray,
                        ),
                        SizedBox(width: 4),
                        Text(
                          experience['location'],
                          style: DesignConstants.bodySmall.copyWith(
                            color: DesignConstants.darkGray,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              Container(
                decoration: BoxDecoration(
                  color: DesignConstants.surfaceGray,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: PopupMenuButton<String>(
                  icon: Icon(
                    Icons.more_vert,
                    color: DesignConstants.darkGray,
                    size: 20,
                  ),
                  onSelected: (String action) {
                    if (action == 'edit') {
                      _editExperience(index);
                    } else if (action == 'delete') {
                      _deleteExperience(index);
                    }
                  },
                  itemBuilder: (BuildContext context) => [
                    PopupMenuItem<String>(
                      value: 'edit',
                      child: Row(
                        children: [
                          Icon(Icons.edit_outlined, size: 16, color: DesignConstants.modernPrimaryBlue),
                          SizedBox(width: 8),
                          Text(
                            'Uredi',
                            style: DesignConstants.bodyMedium.copyWith(
                              color: DesignConstants.black,
                            ),
                          ),
                        ],
                      ),
                    ),
                    PopupMenuItem<String>(
                      value: 'delete',
                      child: Row(
                        children: [
                          Icon(Icons.delete_outline, size: 16, color: DesignConstants.errorRed),
                          SizedBox(width: 8),
                          Text(
                            'Ukloni',
                            style: DesignConstants.bodyMedium.copyWith(
                              color: DesignConstants.errorRed,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          
          SizedBox(height: 16),
          
          Container(
            padding: EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: DesignConstants.surfaceGray,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: DesignConstants.lightGray,
                width: 1,
              ),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.schedule_outlined,
                  size: 18,
                  color: DesignConstants.modernPrimaryBlue,
                ),
                SizedBox(width: 12),
                Expanded(
                  child: Text(
                    dateRange,
                    style: DesignConstants.bodyMedium.copyWith(
                      color: DesignConstants.black,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
                if (experience['isCurrentJob'])
                  Container(
                    padding: EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: DesignConstants.successGreen.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: DesignConstants.successGreen.withOpacity(0.3),
                        width: 1,
                      ),
                    ),
                    child: Text(
                      'Trenutno',
                      style: DesignConstants.captionMedium.copyWith(
                        fontWeight: FontWeight.w600,
                        color: DesignConstants.successGreen,
                      ),
                    ),
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }
  
  Widget _buildModernAddExperienceButton() {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: DesignConstants.modernPrimaryBlue,
          width: 2,
        ),
      ),
      child: TextButton.icon(
        onPressed: _addNewExperience,
        icon: Icon(
          Icons.add_circle_outline,
          size: 22,
          color: DesignConstants.modernPrimaryBlue,
        ),
        label: Text(
          'Dodaj novo radno iskustvo',
          style: DesignConstants.bodyLarge.copyWith(
            fontWeight: FontWeight.w600,
            color: DesignConstants.modernPrimaryBlue,
          ),
        ),
        style: TextButton.styleFrom(
          padding: EdgeInsets.symmetric(vertical: 18, horizontal: 20),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
        ),
      ),
    );
  }

  Widget _buildFloatingSaveButton() {
    return Positioned(
      bottom: 30,
      left: 20,
      right: 20,
      child: Container(
        decoration: BoxDecoration(
          boxShadow: [
            BoxShadow(
              color: DesignConstants.modernPrimaryBlue.withOpacity(0.3),
              blurRadius: 20,
              offset: Offset(0, 8),
            ),
          ],
        ),
        child: ElevatedButton(
          onPressed: _saveChanges,
          style: ElevatedButton.styleFrom(
            backgroundColor: DesignConstants.modernPrimaryBlue,
            foregroundColor: Colors.white,
            elevation: 0,
            shadowColor: Colors.transparent,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
            ),
            padding: EdgeInsets.symmetric(vertical: 18),
            minimumSize: Size(double.infinity, 56),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.save_outlined, size: 22),
              SizedBox(width: 12),
              Text(
                'Spremi promjene',
                style: DesignConstants.buttonLarge,
              ),
            ],
          ),
        ),
      ),
    );
  }
  
  String _formatDateRange(Map<String, dynamic> experience) {
    String startMonth = _months[experience['startMonth'] - 1];
    String startYear = experience['startYear'].toString();
    
    if (experience['isCurrentJob']) {
      return '$startMonth $startYear - Trenutno';
    } else {
      String endMonth = _months[experience['endMonth'] - 1];
      String endYear = experience['endYear'].toString();
      return '$startMonth $startYear - $endMonth $endYear';
    }
  }
  
  void _editExperience(int index) async {
    final result = await Navigator.push<Map<String, dynamic>>(
      context,
      MaterialPageRoute(
        builder: (context) => AddWorkExperienceScreen(
          existingExperience: _workExperiences[index],
          editIndex: index,
        ),
      ),
    );
    
    if (result != null) {
      setState(() {
        _workExperiences[index] = result['experienceData'];
        _hasChanges = true;
      });
      
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              Icon(Icons.check_circle_outline, color: Colors.white, size: 22),
              SizedBox(width: 12),
              Text(
                'Radno iskustvo uspješno ažurirano!',
                style: DesignConstants.bodyLarge.copyWith(color: Colors.white),
              ),
            ],
          ),
          backgroundColor: DesignConstants.successGreen,
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          margin: EdgeInsets.all(20),
        ),
      );
    }
  }
  
  void _addNewExperience() async {
    final result = await Navigator.push<Map<String, dynamic>>(
      context,
      MaterialPageRoute(
        builder: (context) => AddWorkExperienceScreen(),
      ),
    );
    
    if (result != null) {
      setState(() {
        _workExperiences.add(result['experienceData']);
        _hasChanges = true;
      });
      
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              Icon(Icons.check_circle_outline, color: Colors.white, size: 22),
              SizedBox(width: 12),
              Text(
                'Novo radno iskustvo uspješno dodano!',
                style: DesignConstants.bodyLarge.copyWith(color: Colors.white),
              ),
            ],
          ),
          backgroundColor: DesignConstants.successGreen,
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          margin: EdgeInsets.all(20),
        ),
      );
    }
  }
  
  void _deleteExperience(int index) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          title: Text(
            'Potvrdi brisanje',
            style: DesignConstants.titleSmall.copyWith(
              color: DesignConstants.black,
            ),
          ),
          content: Text(
            'Da li ste sigurni da želite ukloniti ovo radno iskustvo?',
            style: DesignConstants.bodyMedium.copyWith(
              color: DesignConstants.darkGray,
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(
                'Otkaži',
                style: DesignConstants.bodyMedium.copyWith(
                  color: DesignConstants.darkGray,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
            TextButton(
              onPressed: () {
                setState(() {
                  _workExperiences.removeAt(index);
                  _hasChanges = true;
                });
                Navigator.of(context).pop();
              },
              child: Text(
                'Ukloni',
                style: DesignConstants.bodyMedium.copyWith(
                  color: DesignConstants.errorRed,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        );
      },
    );
  }
  
  void _saveChanges() {
    // TODO: Save changes to backend/state management
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(Icons.check_circle_outline, color: Colors.white, size: 22),
            SizedBox(width: 12),
            Text(
              'Radno iskustvo uspješno ažurirano!',
              style: DesignConstants.bodyLarge.copyWith(color: Colors.white),
            ),
          ],
        ),
        backgroundColor: DesignConstants.successGreen,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        margin: EdgeInsets.all(20),
      ),
    );
    
    Navigator.pop(context, true);
  }
}
