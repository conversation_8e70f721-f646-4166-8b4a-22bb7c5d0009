import 'package:flutter/material.dart';
import '../constants/design_constants.dart';

class EditEducationScreen extends StatefulWidget {
  @override
  _EditEducationScreenState createState() => _EditEducationScreenState();
}

class _EditEducationScreenState extends State<EditEducationScreen> {
  final _formKey = GlobalKey<FormState>();
  final _fieldOfStudyController = TextEditingController();
  final _schoolNameController = TextEditingController();
  final _locationController = TextEditingController();
  
  String? _selectedLevel;
  int? _startMonth;
  int? _startYear;
  int? _endMonth;
  int? _endYear;
  
  bool _hasChanges = false;

  final Map<String, String> _educationLevels = {
    'osnovno': 'Osnovno obrazovanje',
    'srednje': 'Srednje obrazovanje',
    'visoko': 'Vis<PERSON> obrazovanje',
    'master': 'Master/Magistar',
    'doktor': '<PERSON><PERSON><PERSON>ke studije',
  };

  final List<String> _months = [
    'Januar', 'Februar', 'Mart', 'April', 'Maj', 'Jun<PERSON>',
    'Juli', 'August', 'Septembar', 'Oktobar', 'Novembar', 'Decembar'
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: DesignConstants.modernPrimaryGradient,
        ),
        child: SafeArea(
          child: Stack(
            children: [
              Column(
                children: [
                  _buildModernAppBar(),
                  Expanded(
                    child: Container(
                      margin: EdgeInsets.only(top: 20),
                      decoration: BoxDecoration(
                        color: DesignConstants.backgroundGray,
                        borderRadius: BorderRadius.vertical(
                          top: Radius.circular(30),
                        ),
                      ),
                      child: SingleChildScrollView(
                        padding: EdgeInsets.fromLTRB(20, 30, 20, 100),
                        child: Form(
                          key: _formKey,
                          child: Column(
                            children: [
                              _buildModernHeader(),
                              SizedBox(height: 24),
                              _buildModernFormCard(),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
              _buildFloatingSaveButton(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildModernAppBar() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 20, vertical: 15),
      child: Row(
        children: [
          Container(
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.2),
              borderRadius: BorderRadius.circular(12),
            ),
            child: IconButton(
              onPressed: () => Navigator.pop(context),
              icon: Icon(
                Icons.arrow_back_ios_new,
                color: Colors.white,
                size: 20,
              ),
            ),
          ),
          Expanded(
            child: Center(
              child: Text(
                'Obrazovanje',
                style: DesignConstants.titleMedium.copyWith(
                  color: Colors.white,
                ),
              ),
            ),
          ),
          SizedBox(width: 48),
        ],
      ),
    );
  }

  Widget _buildModernHeader() {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: DesignConstants.shadowMedium,
        border: Border.all(
          color: DesignConstants.borderGray,
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: EdgeInsets.all(16),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  DesignConstants.modernPrimaryBlue.withOpacity(0.1),
                  DesignConstants.modernPrimaryBlue.withOpacity(0.05),
                ],
              ),
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: DesignConstants.modernPrimaryBlue.withOpacity(0.2),
                width: 1,
              ),
            ),
            child: Icon(
              Icons.school_outlined,
              color: DesignConstants.modernPrimaryBlue,
              size: 28,
            ),
          ),
          SizedBox(width: 20),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Obrazovni podaci',
                  style: DesignConstants.titleSmall.copyWith(
                    color: DesignConstants.black,
                  ),
                ),
                SizedBox(height: 8),
                Text(
                  'Unesite informacije o vašem obrazovanju',
                  style: DesignConstants.bodyMedium.copyWith(
                    color: DesignConstants.darkGray,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildModernFormCard() {
    return Container(
      padding: EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: DesignConstants.shadowMedium,
        border: Border.all(
          color: DesignConstants.borderGray,
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildModernEducationLevelDropdown(),
          SizedBox(height: 24),
          _buildModernTextField(
            label: 'Smjer studiranja / Oblast',
            controller: _fieldOfStudyController,
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Molimo unesite smjer studiranja';
              }
              return null;
            },
          ),
          SizedBox(height: 24),
          _buildModernTextField(
            label: 'Naziv škole / Univerziteta',
            controller: _schoolNameController,
          ),
          SizedBox(height: 24),
          _buildModernTextField(
            label: 'Mjesto',
            controller: _locationController,
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Molimo unesite mjesto';
              }
              return null;
            },
          ),
          SizedBox(height: 24),
          _buildModernDateRange(),
        ],
      ),
    );
  }

  Widget _buildModernEducationLevelDropdown() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Nivo obrazovanja',
          style: DesignConstants.labelMedium.copyWith(
            color: DesignConstants.black,
          ),
        ),
        SizedBox(height: 8),
        Container(
          decoration: BoxDecoration(
            color: DesignConstants.surfaceGray,
            borderRadius: BorderRadius.circular(16),
            border: Border.all(
              color: DesignConstants.lightGray,
              width: 1,
            ),
          ),
          child: DropdownButtonFormField<String>(
            value: _selectedLevel,
            onChanged: (String? newValue) {
              if (newValue != _selectedLevel) {
                setState(() {
                  _selectedLevel = newValue;
                  _hasChanges = true;
                });
              }
            },
            decoration: InputDecoration(
              border: InputBorder.none,
              contentPadding: EdgeInsets.symmetric(horizontal: 20, vertical: 16),
            ),
            style: DesignConstants.bodyLarge.copyWith(
              color: DesignConstants.black,
            ),
            items: _educationLevels.entries.map((entry) {
              return DropdownMenuItem<String>(
                value: entry.key,
                child: Text(entry.value),
              );
            }).toList(),
          ),
        ),
      ],
    );
  }

  Widget _buildModernTextField({
    required String label,
    required TextEditingController controller,
    String? Function(String?)? validator,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: DesignConstants.labelMedium.copyWith(
            color: DesignConstants.black,
          ),
        ),
        SizedBox(height: 8),
        Container(
          decoration: BoxDecoration(
            color: DesignConstants.surfaceGray,
            borderRadius: BorderRadius.circular(16),
            border: Border.all(
              color: DesignConstants.lightGray,
              width: 1,
            ),
          ),
          child: TextFormField(
            controller: controller,
            validator: validator,
            style: DesignConstants.bodyLarge.copyWith(
              color: DesignConstants.black,
            ),
            onChanged: (value) {
              setState(() {
                _hasChanges = true;
              });
            },
            decoration: InputDecoration(
              border: InputBorder.none,
              contentPadding: EdgeInsets.symmetric(horizontal: 20, vertical: 16),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildModernDateRange() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Period studiranja',
          style: DesignConstants.labelMedium.copyWith(
            color: DesignConstants.black,
          ),
        ),
        SizedBox(height: 16),
        Column(
          children: [
            _buildModernDateDropdown('Od', _startMonth, _startYear, true),
            SizedBox(height: 20),
            _buildModernDateDropdown('Do', _endMonth, _endYear, false),
          ],
        ),
      ],
    );
  }

  Widget _buildModernDateDropdown(String label, int? month, int? year, bool isStart) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: DesignConstants.captionMedium.copyWith(
            color: DesignConstants.darkGray,
          ),
        ),
        SizedBox(height: 8),
        Row(
          children: [
            Expanded(
              flex: 3,
              child: Container(
                decoration: BoxDecoration(
                  color: DesignConstants.surfaceGray,
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(
                    color: DesignConstants.lightGray,
                    width: 1,
                  ),
                ),
                child: DropdownButtonFormField<int>(
                  value: month,
                  onChanged: (int? value) {
                    setState(() {
                      if (isStart) {
                        _startMonth = value;
                      } else {
                        _endMonth = value;
                      }
                      _hasChanges = true;
                    });
                  },
                  decoration: InputDecoration(
                    border: InputBorder.none,
                    contentPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                  ),
                  style: DesignConstants.bodySmall.copyWith(
                    color: DesignConstants.black,
                  ),
                  items: List.generate(12, (index) {
                    return DropdownMenuItem<int>(
                      value: index + 1,
                      child: Text(
                        _months[index],
                        overflow: TextOverflow.ellipsis,
                      ),
                    );
                  }),
                ),
              ),
            ),
            SizedBox(width: 12),
            Expanded(
              flex: 2,
              child: Container(
                decoration: BoxDecoration(
                  color: DesignConstants.surfaceGray,
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(
                    color: DesignConstants.lightGray,
                    width: 1,
                  ),
                ),
                child: DropdownButtonFormField<int>(
                  value: year,
                  onChanged: (int? value) {
                    setState(() {
                      if (isStart) {
                        _startYear = value;
                      } else {
                        _endYear = value;
                      }
                      _hasChanges = true;
                    });
                  },
                  decoration: InputDecoration(
                    border: InputBorder.none,
                    contentPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                  ),
                  style: DesignConstants.bodySmall.copyWith(
                    color: DesignConstants.black,
                  ),
                  items: List.generate(50, (index) {
                    int year = DateTime.now().year - index;
                    return DropdownMenuItem<int>(
                      value: year,
                      child: Text(year.toString()),
                    );
                  }),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildFloatingSaveButton() {
    return Positioned(
      bottom: 30,
      left: 20,
      right: 20,
      child: Container(
        decoration: BoxDecoration(
          boxShadow: [
            BoxShadow(
              color: DesignConstants.modernPrimaryBlue.withOpacity(0.3),
              blurRadius: 20,
              offset: Offset(0, 8),
            ),
          ],
        ),
        child: ElevatedButton(
          onPressed: _hasChanges ? _saveChanges : null,
          style: ElevatedButton.styleFrom(
            backgroundColor: DesignConstants.modernPrimaryBlue,
            foregroundColor: Colors.white,
            elevation: 0,
            shadowColor: Colors.transparent,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
            ),
            padding: EdgeInsets.symmetric(vertical: 18),
            minimumSize: Size(double.infinity, 56),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.save_outlined, size: 22),
              SizedBox(width: 12),
              Text(
                'Spremi promjene',
                style: DesignConstants.buttonLarge,
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _saveChanges() {
    if (_formKey.currentState!.validate()) {
      // TODO: Save changes to backend/state management
      
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              Icon(Icons.check_circle_outline, color: Colors.white, size: 22),
              SizedBox(width: 12),
              Text(
                'Obrazovanje uspješno ažurirano!',
                style: DesignConstants.bodyLarge.copyWith(color: Colors.white),
              ),
            ],
          ),
          backgroundColor: DesignConstants.successGreen,
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          margin: EdgeInsets.all(20),
        ),
      );
      
      Navigator.pop(context, true);
    }
  }

  @override
  void dispose() {
    _fieldOfStudyController.dispose();
    _schoolNameController.dispose();
    _locationController.dispose();
    super.dispose();
  }
}
