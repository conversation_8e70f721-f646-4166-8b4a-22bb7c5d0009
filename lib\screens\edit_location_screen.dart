import 'package:flutter/material.dart';
import '../constants/design_constants.dart';

class EditLocationScreen extends StatefulWidget {
  @override
  _EditLocationScreenState createState() => _EditLocationScreenState();
}

class _EditLocationScreenState extends State<EditLocationScreen> {
  final _formKey = GlobalKey<FormState>();
  
  String? _selectedCountry = 'RS';
  late TextEditingController _cityController;
  
  bool _hasChanges = false;
  
  final Map<String, String> _countries = {
    'RS': 'Srbija',
    'HR': 'Hrvatska',
    'BA': 'Bosna i Hercegovina',
    'ME': 'Crna Gora',
    'MK': 'Sjeverna Makedonija',
    'SI': 'Slovenija',
    'DE': 'Njemač<PERSON>',
    'AT': 'Austrija',
    'CH': 'Švicarska',
  };
  
  @override
  void initState() {
    super.initState();
    
    _cityController = TextEditingController(text: 'Novi Sad');
    _cityController.addListener(_onFieldChanged);
  }
  
  @override
  void dispose() {
    _cityController.dispose();
    super.dispose();
  }
  
  void _onFieldChanged() {
    if (!_hasChanges) {
      setState(() {
        _hasChanges = true;
      });
    }
  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.center,
            colors: [
              DesignConstants.modernPrimaryBlue,
              DesignConstants.modernPrimaryBlue.withOpacity(0.1),
            ],
            stops: [0.0, 0.3],
          ),
        ),
        child: SafeArea(
          child: Stack(
            children: [
              Column(
                children: [
                  // Custom AppBar with gradient
                  Container(
                    padding: EdgeInsets.symmetric(horizontal: 20, vertical: 16),
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.15),
                      border: Border(
                        bottom: BorderSide(
                          color: Colors.white.withOpacity(0.2),
                          width: 1,
                        ),
                      ),
                    ),
                    child: Row(
                      children: [
                        Container(
                          decoration: BoxDecoration(
                            color: Colors.white.withOpacity(0.2),
                            borderRadius: BorderRadius.circular(DesignConstants.radiusMedium),
                            border: Border.all(
                              color: Colors.white.withOpacity(0.3),
                              width: 1,
                            ),
                          ),
                          child: IconButton(
                            onPressed: () => Navigator.pop(context),
                            icon: Icon(
                              Icons.arrow_back_ios,
                              color: DesignConstants.white,
                              size: DesignConstants.iconMedium,
                            ),
                            padding: EdgeInsets.all(8),
                          ),
                        ),
                        Expanded(
                          child: Text(
                            'Uredi lokaciju',
                            textAlign: TextAlign.center,
                            style: DesignConstants.titleMedium.copyWith(
                              color: DesignConstants.white,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                        // Invisible container for balance
                        Container(width: 48, height: 48),
                      ],
                    ),
                  ),
                  // Content area
                  Expanded(
                    child: Container(
                      width: double.infinity,
                      decoration: BoxDecoration(
                        color: DesignConstants.surfaceGray,
                        borderRadius: BorderRadius.only(
                          topLeft: Radius.circular(DesignConstants.radiusXLarge),
                          topRight: Radius.circular(DesignConstants.radiusXLarge),
                        ),
                      ),
                      child: SingleChildScrollView(
                        padding: EdgeInsets.fromLTRB(20, 20, 20, 100), // Bottom padding for floating button
                        child: Form(
                          key: _formKey,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              _buildModernHeader(),
                              SizedBox(height: DesignConstants.spaceXXL),
                              _buildModernFormCard(),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
              
              // Floating Save Button
              if (_hasChanges)
                Positioned(
                  left: 20,
                  right: 20,
                  bottom: 20,
                  child: _buildFloatingSaveButton(),
                ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildModernHeader() {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(DesignConstants.spaceXXL),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            DesignConstants.white,
            DesignConstants.modernPrimaryBlue.withOpacity(0.02),
          ],
        ),
        borderRadius: BorderRadius.circular(DesignConstants.radiusXXLarge),
        border: Border.all(
          color: DesignConstants.modernPrimaryBlue.withOpacity(0.15),
          width: 1.5,
        ),
        boxShadow: [
          BoxShadow(
            color: DesignConstants.modernPrimaryBlue.withOpacity(0.08),
            blurRadius: 20,
            offset: Offset(0, 8),
            spreadRadius: 1,
          ),
          BoxShadow(
            color: Colors.black.withOpacity(0.03),
            blurRadius: 8,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: EdgeInsets.all(DesignConstants.spaceM),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      DesignConstants.modernPrimaryBlue.withOpacity(0.1),
                      DesignConstants.modernPrimaryBlue.withOpacity(0.05),
                    ],
                  ),
                  borderRadius: BorderRadius.circular(DesignConstants.radiusLarge),
                ),
                child: Icon(
                  Icons.location_on_outlined,
                  color: DesignConstants.modernPrimaryBlue,
                  size: DesignConstants.iconLarge,
                ),
              ),
              SizedBox(width: DesignConstants.spaceL),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Lokacija',
                      style: DesignConstants.titleMedium.copyWith(
                        color: DesignConstants.black,
                      ),
                    ),
                    SizedBox(height: DesignConstants.spaceXS),
                    Text(
                      'Uredite svoju trenutnu lokaciju i kontakt podatke',
                      style: DesignConstants.bodyMedium.copyWith(
                        color: DesignConstants.darkGray,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildModernFormCard() {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(DesignConstants.spaceXXL),
      decoration: BoxDecoration(
        color: DesignConstants.white,
        borderRadius: BorderRadius.circular(DesignConstants.radiusXLarge),
        border: Border.all(
          color: DesignConstants.lightGray.withOpacity(0.5),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.04),
            blurRadius: 16,
            offset: Offset(0, 4),
          ),
          BoxShadow(
            color: Colors.black.withOpacity(0.02),
            blurRadius: 4,
            offset: Offset(0, 1),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildModernCountryDropdown(),
          SizedBox(height: DesignConstants.spaceXXL),
          _buildModernCityField(),
        ],
      ),
    );
  }

  Widget _buildFloatingSaveButton() {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        gradient: DesignConstants.modernPrimaryGradient,
        borderRadius: BorderRadius.circular(DesignConstants.radiusLarge),
        boxShadow: [
          BoxShadow(
            color: DesignConstants.modernPrimaryBlue.withOpacity(0.4),
            blurRadius: 20,
            offset: Offset(0, 8),
          ),
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: _saveChanges,
          borderRadius: BorderRadius.circular(DesignConstants.radiusLarge),
          child: Container(
            padding: EdgeInsets.symmetric(
              vertical: DesignConstants.spaceL + 2,
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.check_circle_outline,
                  color: DesignConstants.white,
                  size: DesignConstants.iconMedium,
                ),
                SizedBox(width: DesignConstants.spaceS),
                Text(
                  'Spremi promjene',
                  style: DesignConstants.buttonLarge.copyWith(
                    color: DesignConstants.white,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildModernCountryDropdown() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Container(
              padding: EdgeInsets.all(6),
              decoration: BoxDecoration(
                color: DesignConstants.modernPrimaryBlue.withOpacity(0.1),
                borderRadius: BorderRadius.circular(6),
              ),
              child: Icon(
                Icons.public_outlined,
                size: 16,
                color: DesignConstants.modernPrimaryBlue,
              ),
            ),
            SizedBox(width: DesignConstants.spaceS),
            Text(
              'Država',
              style: DesignConstants.labelMedium.copyWith(
                color: DesignConstants.black,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
        SizedBox(height: DesignConstants.spaceM),
        Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(DesignConstants.radiusMedium),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.02),
                blurRadius: 4,
                offset: Offset(0, 2),
              ),
            ],
          ),
          child: DropdownButtonFormField<String>(
            value: _selectedCountry,
            onChanged: (String? newValue) {
              if (newValue != _selectedCountry) {
                setState(() {
                  _selectedCountry = newValue;
                  _hasChanges = true;
                });
              }
            },
            decoration: InputDecoration(
              filled: true,
              fillColor: DesignConstants.surfaceGray,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(DesignConstants.radiusMedium),
                borderSide: BorderSide(
                  color: DesignConstants.lightGray.withOpacity(0.3),
                  width: 1.5,
                ),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(DesignConstants.radiusMedium),
                borderSide: BorderSide(
                  color: DesignConstants.lightGray.withOpacity(0.3),
                  width: 1.5,
                ),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(DesignConstants.radiusMedium),
                borderSide: BorderSide(
                  color: DesignConstants.modernPrimaryBlue,
                  width: 2,
                ),
              ),
              contentPadding: EdgeInsets.symmetric(
                horizontal: DesignConstants.spaceL,
                vertical: DesignConstants.spaceM + 2,
              ),
            ),
            items: _countries.entries.map((entry) {
              return DropdownMenuItem<String>(
                value: entry.key,
                child: Text(
                  entry.value,
                  style: DesignConstants.bodyLarge.copyWith(
                    color: DesignConstants.black,
                  ),
                ),
              );
            }).toList(),
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Molimo odaberite državu';
              }
              return null;
            },
          ),
        ),
      ],
    );
  }

  Widget _buildModernCityField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Container(
              padding: EdgeInsets.all(6),
              decoration: BoxDecoration(
                color: DesignConstants.modernPrimaryBlue.withOpacity(0.1),
                borderRadius: BorderRadius.circular(6),
              ),
              child: Icon(
                Icons.location_city_outlined,
                size: 16,
                color: DesignConstants.modernPrimaryBlue,
              ),
            ),
            SizedBox(width: DesignConstants.spaceS),
            Text(
              'Grad/Mjesto',
              style: DesignConstants.labelMedium.copyWith(
                color: DesignConstants.black,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
        SizedBox(height: DesignConstants.spaceM),
        Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(DesignConstants.radiusMedium),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.02),
                blurRadius: 4,
                offset: Offset(0, 2),
              ),
            ],
          ),
          child: TextFormField(
            controller: _cityController,
            style: DesignConstants.bodyLarge.copyWith(
              color: DesignConstants.black,
            ),
            decoration: InputDecoration(
              filled: true,
              fillColor: DesignConstants.surfaceGray,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(DesignConstants.radiusMedium),
                borderSide: BorderSide(
                  color: DesignConstants.lightGray.withOpacity(0.3),
                  width: 1.5,
                ),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(DesignConstants.radiusMedium),
                borderSide: BorderSide(
                  color: DesignConstants.lightGray.withOpacity(0.3),
                  width: 1.5,
                ),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(DesignConstants.radiusMedium),
                borderSide: BorderSide(
                  color: DesignConstants.modernPrimaryBlue,
                  width: 2,
                ),
              ),
              errorBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(DesignConstants.radiusMedium),
                borderSide: BorderSide(
                  color: DesignConstants.modernErrorRed,
                  width: 1.5,
                ),
              ),
              focusedErrorBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(DesignConstants.radiusMedium),
                borderSide: BorderSide(
                  color: DesignConstants.modernErrorRed,
                  width: 2,
                ),
              ),
              contentPadding: EdgeInsets.symmetric(
                horizontal: DesignConstants.spaceL,
                vertical: DesignConstants.spaceM + 2,
              ),
              hintStyle: DesignConstants.bodyMedium.copyWith(
                color: DesignConstants.darkGray.withOpacity(0.6),
              ),
            ),
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Molimo unesite naziv grada/mjesta';
              }
              return null;
            },
          ),
        ),
      ],
    );
  }

  void _saveChanges() {
    if (_formKey.currentState!.validate()) {
      // TODO: Save changes to backend/state management
      
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Container(
            padding: EdgeInsets.symmetric(vertical: 4),
            child: Row(
              children: [
                Container(
                  padding: EdgeInsets.all(6),
                  decoration: BoxDecoration(
                    color: DesignConstants.white.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Icon(
                    Icons.check_circle,
                    color: DesignConstants.white,
                    size: 16,
                  ),
                ),
                SizedBox(width: DesignConstants.spaceM),
                Expanded(
                  child: Text(
                    'Lokacija uspješno ažurirana!',
                    style: DesignConstants.labelMedium.copyWith(
                      color: DesignConstants.white,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
          ),
          backgroundColor: DesignConstants.modernSuccessGreen,
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(DesignConstants.radiusMedium),
          ),
          margin: EdgeInsets.all(DesignConstants.spaceL),
          elevation: 8,
          duration: Duration(seconds: 3),
        ),
      );
      
      Navigator.pop(context, true);
    }
  }
}