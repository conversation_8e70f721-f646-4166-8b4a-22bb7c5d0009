# Developer Guide - Mock Data Documentation

## Pregled aplikacije

Aplikacija je **job board platforma** (BalkanPosao) za traženje poslova u Njemačkoj, Austriji, Švajcarskoj i Balkanskim zemljama. Trenutno koristi **mock/test podatke** za sve funkcionalnosti koje trebaju biti zamijenjene sa real time podacima iz baze.

## 🔥 GLAVNE FUNKCIONALNOSTI

### 1. **Korisničke funkcije**
- Registracija/Login korisnika
- Kreiranje i upravljanje profilom 
- Onboarding process (6 koraka)
- Pregled i editovanje profila

### 2. **Job Management**
- Pregled poslova sa filterima
- Detaljne stranice poslova
- Aplikovanje na poslove
- Praćenje statusa prijava

### 3. **Komunikacija**
- Chat sa poslodavcima
- Pozivnice od kompanija
- Notifikacije

---

## 📊 MOCK DATA - DETALJNO MAPIRANJE

### **USER PROFILES & AUTHENTICATION**

#### `lib/screens/profile_screen_complete.dart` (LINES 30-90)
```dart
// MOCK USER - ZAMIJENITI SA DYNAMIC USER DATA
final Map<String, dynamic> mockUserData = {
  'firstName': 'Stefan',                    // → API: user.firstName
  'lastName': 'Jovanović',                  // → API: user.lastName  
  'phoneNumber': '+381 62 987 654',         // → API: user.phone
  'country': 'RS',                          // → API: user.countryCode
  'location': 'Novi Sad',                   // → API: user.city
  'drivingLicenses': ['B', 'C', 'CE'],      // → API: user.drivingLicenses[]
  'germanLevel': 'b2',                      // → API: user.languages.german
  'englishLevel': 'b1',                     // → API: user.languages.english
};

// MOCK EDUCATION DATA
final Map<String, dynamic> mockEducation = {
  'level': 'srednja',                       // → API: user.education.level
  'fieldOfStudy': 'Mašinski tehničar',      // → API: user.education.field
  'schoolName': 'Tehnička škola "Mihajlo Pupin"',  // → API: user.education.school
  'location': 'Novi Sad',                  // → API: user.education.location
  'startMonth': 9, 'startYear': 2016,      // → API: user.education.startDate
  'endMonth': 6, 'endYear': 2020,          // → API: user.education.endDate
};

// MOCK WORK EXPERIENCE DATA (2 pozicije)
final List<Map<String, dynamic>> mockWorkExperience = [
  {
    'position': 'Mašinski radnik',          // → API: user.workExperience[0].position
    'company': 'Delta Holding d.o.o.',     // → API: user.workExperience[0].company
    'location': 'Novi Sad',                // → API: user.workExperience[0].location
    'startMonth': 8, 'startYear': 2020,    // → API: user.workExperience[0].startDate
    'endMonth': 3, 'endYear': 2022,        // → API: user.workExperience[0].endDate
    'isCurrentJob': false,                 // → API: user.workExperience[0].isCurrent
  },
  {
    'position': 'CNC operater',             // → API: user.workExperience[1].position
    'company': 'Fiat Chrysler Automobiles', // → API: user.workExperience[1].company
    'location': 'Kragujevac',              // → API: user.workExperience[1].location
    'startMonth': 4, 'startYear': 2022,    // → API: user.workExperience[1].startDate
    'isCurrentJob': true,                  // → API: user.workExperience[1].isCurrent
  }
];
```

#### `lib/screens/register_screen.dart` (LINES 20-35)
```dart
// BALKANSKE ZEMLJE - ZAMIJENITI SA API CALL
final Map<String, Map<String, String>> balkanCountries = {
  'BA': {'name': 'Bosnia and Herzegovina', 'code': '+387', 'flag': '🇧🇦'},  // → API: countries table
  'HR': {'name': 'Croatia', 'code': '+385', 'flag': '🇭🇷'},
  'RS': {'name': 'Serbia', 'code': '+381', 'flag': '🇷🇸'},
  'ME': {'name': 'Montenegro', 'code': '+382', 'flag': '🇲🇪'},
  'MK': {'name': 'North Macedonia', 'code': '+389', 'flag': '🇲🇰'},
  'SI': {'name': 'Slovenia', 'code': '+386', 'flag': '🇸🇮'},
  'AL': {'name': 'Albania', 'code': '+355', 'flag': '🇦🇱'},
};
```

---

### **JOB LISTINGS**

#### `lib/screens/jobs_screen.dart` (LINES 25-120)
```dart
// MOCK FEATURED JOBS - ZAMIJENITI SA API: GET /api/jobs?featured=true
final List<Map<String, dynamic>> featuredJobs = [
  {
    'id': '1',                              // → API: job.id
    'title': 'Senior Software Engineer',   // → API: job.title
    'company': 'TechCorp GmbH',            // → API: job.company.name
    'country': 'Njemačka',                 // → API: job.location.country
    'city': 'Berlin',                      // → API: job.location.city
    'salary': '€4,500 - €6,000',          // → API: job.salaryRange
    'logo': '🏢',                          // → API: job.company.logo
    'countryFlag': '🇩🇪',                  // → API: job.location.countryFlag
  },
  // +2 more featured jobs...
];

// MOCK REGULAR JOBS - ZAMIJENITI SA API: GET /api/jobs
final List<Map<String, dynamic>> regularJobs = [
  {
    'id': '4',                              // → API: job.id
    'title': 'Frontend Developer',         // → API: job.title
    'company': 'WebSolutions',             // → API: job.company.name
    'country': 'Njemačka',                 // → API: job.location.country
    'city': 'Hamburg',                     // → API: job.location.city
    'salary': '€3,000 - €4,200',          // → API: job.salaryRange
    'type': 'Puno radno vrijeme',          // → API: job.workType
  },
  // +3 more regular jobs...
];
```

#### `lib/screens/job_details_screen.dart` (LINES 15-80)
```dart
// MOCK JOB DETAILS - ZAMIJENITI SA API: GET /api/jobs/{id}
final Map<String, dynamic> mockJobDetails = {
  'id': '4',                                        // → API: job.id
  'title': 'Frontend Developer',                   // → API: job.title
  'company': 'WebSolutions',                       // → API: job.company.name
  'country': 'Njemačka',                           // → API: job.location.country
  'city': 'Hamburg',                               // → API: job.location.city
  'salary': '€3,000 - €4,200',                    // → API: job.salaryRange
  'type': 'Puno radno vrijeme',                    // → API: job.workType
  'description': 'Pridružite se našem dinamičnom timu...', // → API: job.description
  
  // MOCK REQUIREMENTS - ZAMIJENITI SA API: job.requirements[]
  'requirements': [
    'Njemački jezik - nivo B2 ili viši',           // → API: job.requirements[0].text
    'Vozačka dozvola kategorije B',                 // → API: job.requirements[1].text
    'Minimum 2 godine iskustva u React.js',        // → API: job.requirements[2].text
    'Poznavanje TypeScript-a',                     // → API: job.requirements[3].text
    'Iskustvo sa Tailwind CSS',                    // → API: job.requirements[4].text
    'Razumijevanje Git workflow-a',                // → API: job.requirements[5].text
    'Komunikacijske vještine',                     // → API: job.requirements[6].text
  ],
  
  // MOCK BENEFITS - ZAMIJENITI SA API: job.benefits[]
  'benefits': [
    'Fleksibilno radno vrijeme',                   // → API: job.benefits[0].text
    'Mogućnost rada od kuće',                      // → API: job.benefits[1].text
    'Privatno zdravstveno osiguranje',             // → API: job.benefits[2].text
    'Godišnji bonus',                              // → API: job.benefits[3].text
    'Besplatna kafa i grickalice',                 // → API: job.benefits[4].text
    'Tim building eventi',                         // → API: job.benefits[5].text
    'Mogućnost napredovanja',                      // → API: job.benefits[6].text
    'Moderni radni prostor',                       // → API: job.benefits[7].text
  ],
};
```

---

### **APPLICATIONS & STATUS TRACKING**

#### `lib/screens/applications_screen.dart` (LINES 20-100)
```dart
// MOCK USER APPLICATIONS - ZAMIJENITI SA API: GET /api/users/{id}/applications
final List<Map<String, dynamic>> mockApplications = [
  {
    'id': '1',                                      // → API: application.id
    'jobTitle': 'Senior Software Engineer',        // → API: application.job.title
    'company': 'TechCorp GmbH',                    // → API: application.job.company.name
    'applicationDate': '15. decembar 2024',        // → API: application.createdAt
    'status': 'interview',                         // → API: application.status
    'salary': '€4,500 - €6,000',                  // → API: application.job.salaryRange
  },
  {
    'id': '2',
    'jobTitle': 'Frontend Developer',              // → API: application.job.title
    'company': 'WebSolutions',                     // → API: application.job.company.name
    'applicationDate': '12. decembar 2024',        // → API: application.createdAt
    'status': 'review',                            // → API: application.status
    'salary': '€3,200 - €4,500',                  // → API: application.job.salaryRange
  },
  // +4 more applications...
];

// APPLICATION STATUS MAPPINGS
final Map<String, Map<String, dynamic>> statusConfig = {
  'review': {'label': 'Pregled', 'color': Colors.orange},           // → API: status enum
  'interview': {'label': 'Intervju', 'color': Colors.blue},         // → API: status enum
  'contract': {'label': 'Ugovor', 'color': Colors.green},           // → API: status enum
  'onboarding': {'label': 'Početak rada', 'color': Colors.purple},  // → API: status enum
  'initial_rejected': {'label': 'Odbijeno', 'color': Colors.red},   // → API: status enum
  'interview_rejected': {'label': 'Nije odabrano', 'color': Colors.grey}, // → API: status enum
};
```

---

### **MESSAGES & CHAT**

#### `lib/screens/messages_screen.dart` (LINES 25-120)
```dart
// MOCK CHAT CONVERSATIONS - ZAMIJENITI SA API: GET /api/users/{id}/conversations
final List<Map<String, dynamic>> mockConversations = [
  {
    'id': '1',                                      // → API: conversation.id
    'companyName': 'TechCorp GmbH',                // → API: conversation.company.name
    'lastMessage': 'Pozdrav! Interesuje nas vaš profil za poziciju Senior Developer-a...', // → API: conversation.lastMessage.text
    'timestamp': '10:30',                          // → API: conversation.lastMessage.createdAt
    'unreadCount': 2,                              // → API: conversation.unreadCount
    'companyLogo': '🏢',                           // → API: conversation.company.logo
    'countryFlag': '🇩🇪',                          // → API: conversation.company.country.flag
    'jobTitle': 'Senior Software Engineer',       // → API: conversation.job.title
    'applicationStatus': 'interview',              // → API: conversation.application.status
  },
  // +7 more conversations...
];
```

#### `lib/screens/chat_screen.dart` (LINES 20-60)
```dart
// MOCK CHAT MESSAGES - ZAMIJENITI SA API: GET /api/conversations/{id}/messages
final List<Map<String, dynamic>> mockMessages = [
  {
    'text': 'Pozdrav! Interesuje nas vaš profil za poziciju Senior Developer-a. Možemo li da zakazemo razgovor?', // → API: message.text
    'isFromCompany': true,                         // → API: message.sender.type
    'timestamp': DateTime.now().subtract(Duration(hours: 2)), // → API: message.createdAt
  },
  {
    'text': 'Zdravo! Hvala vam na interesovanju. Rado bih čuo više detalja o poziciji.', // → API: message.text
    'isFromCompany': false,                        // → API: message.sender.type
    'timestamp': DateTime.now().subtract(Duration(hours: 1, minutes: 30)), // → API: message.createdAt
  },
  // +2 more messages...
];
```

---

### **INVITATIONS**

#### `lib/screens/invitations_screen.dart` (LINES 15-40)
```dart
// MOCK JOB INVITATIONS - ZAMIJENITI SA API: GET /api/users/{id}/invitations
final List<Map<String, dynamic>> mockInvitations = [
  {
    'id': '1',                                      // → API: invitation.id
    'jobTitle': 'Senior Flutter Developer',        // → API: invitation.job.title
    'company': 'TechCorp GmbH',                    // → API: invitation.job.company.name
    'receivedDate': '18. decembar 2024',           // → API: invitation.createdAt
    'status': 'pending',                           // → API: invitation.status
    'logo': '🏢',                                  // → API: invitation.job.company.logo
    'description': 'Kompanija vas poziva na razgovor za poziciju Senior Flutter Developer.', // → API: invitation.message
  },
];
```

---

## 🛠️ IMPLEMENTACIJSKI PLAN

### **1. DATABASE SCHEMA**

**Potrebne tabele:**
- `users` - korisničke informacije, profili
- `companies` - podaci o kompanijama
- `jobs` - oglasi za posao
- `applications` - prijave korisnika 
- `conversations` - chat konverzacije
- `messages` - poruke u chatovima
- `invitations` - pozivnice od kompanija
- `countries` - zemlje sa kodovima
- `work_experience` - radno iskustvo korisnika
- `education` - obrazovanje korisnika

### **2. API ENDPOINTS**

```
Authentication:
POST /api/auth/register
POST /api/auth/login
POST /api/auth/logout

Users:
GET /api/users/{id}
PUT /api/users/{id}
GET /api/users/{id}/profile

Jobs:
GET /api/jobs
GET /api/jobs/{id}
GET /api/jobs?featured=true
POST /api/jobs/{id}/apply

Applications:
GET /api/users/{id}/applications
GET /api/applications/{id}
PUT /api/applications/{id}/status

Messages:
GET /api/users/{id}/conversations
GET /api/conversations/{id}/messages
POST /api/conversations/{id}/messages

Invitations:
GET /api/users/{id}/invitations
PUT /api/invitations/{id}/respond
```

### **3. PRIORITIZACIJA**

**Phase 1 (Kritično):**
1. User registration/login sistem
2. User profile management
3. Job listings sa paginacijom
4. Job application functionality

**Phase 2 (Važno):**
1. Chat sistem (real-time messaging)
2. Application status tracking
3. Job invitation sistem
4. Advanced filtering

**Phase 3 (Nice-to-have):**
1. Push notifikacije
2. Email notifikacije 
3. Advanced search
4. Analytics

---

## 🔥 KLJUČNE FAJLOVE ZA REFACTORING

### **Prioritet 1:**
1. `lib/screens/profile_screen_complete.dart` - user data
2. `lib/screens/jobs_screen.dart` - job listings
3. `lib/screens/applications_screen.dart` - application tracking
4. `lib/screens/register_screen.dart` - authentication

### **Prioritet 2:**  
1. `lib/screens/messages_screen.dart` - chat conversations
2. `lib/screens/chat_screen.dart` - messaging
3. `lib/screens/job_details_screen.dart` - job details
4. `lib/screens/invitations_screen.dart` - invitations

### **Prioritet 3:**
1. `lib/screens/home_screen.dart` - dashboard stats
2. `lib/screens/onboarding_screen.dart` - user onboarding
3. `lib/screens/edit_*_screen.dart` - profile editing

---

## ⚠️ NAPOMENE ZA IMPLEMENTACIJU

1. **State Management**: Preporučujem **Provider** ili **Riverpod** za state management
2. **HTTP Client**: Koristiti **Dio** package za API calls
3. **Local Storage**: **SharedPreferences** za token storage
4. **Real-time Chat**: Implement WebSocket ili **Socket.io** za chat
5. **Image Upload**: Implementirati file upload za company logos i profile pictures
6. **Push Notifications**: **Firebase Cloud Messaging** za notifikacije

**Sve constante i mock data su jasno označeni komentarima `// → API:` za lako mapiranje na backend endpoints.**

---

## 🧹 CLEANUP REZULTATI

### **Uklonjeni duplikati (identični fajlovi):**
- ❌ `lib/screens/add_work_experience_screen_modern.dart` (identičan sa osnovnim)
- ❌ `lib/screens/edit_driving_license_screen_new.dart` (identičan sa osnovnim)
- ❌ `lib/screens/edit_languages_screen_new.dart` (identičan sa osnovnim)  
- ❌ `lib/screens/edit_work_experience_screen_modern.dart` (identičan sa osnovnim)

### **Zadržani fajlovi (imaju razlike):**
- ✅ `lib/screens/edit_education_screen_modern.dart` - ima dodatno polje "Naziv škole"
- ✅ `lib/screens/profile_screen_new.dart` - aktivna verzija u main_screen.dart
- ✅ `lib/screens/profile_screen_complete.dart` - mock profil za Stefan Jovanović

### **Struktura projekta (nakon cleanup-a):**
```
lib/screens/ (32 fajla)
├── Core screens (aktivne):
│   ├── main_screen.dart
│   ├── home_screen.dart  
│   ├── jobs_screen.dart
│   ├── applications_screen.dart
│   ├── messages_screen.dart
│   └── profile_screen_new.dart (aktivni profil)
├── Job management:
│   ├── job_details_screen.dart
│   ├── applied_job_details_screen.dart
│   └── application_details_screen.dart
├── Profile & Onboarding:
│   ├── onboarding_screen.dart
│   ├── profile_screen_complete.dart (mock data)
│   └── edit_*_screen.dart fajlovi
└── Auth & Setup:
    ├── login_screen.dart
    ├── register_screen.dart
    └── splash_screen.dart
```