// MOCKUP CODE - UI/UX Dizajn za developera
// Ovo je mockup kod koji pokazuje kako da se implementiraju 
// Step 5 (Obrazovanje) i Step 6 (Radno iskustvo) u onboarding procesu

import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class OnboardingEducationExperienceMockup extends StatefulWidget {
  @override
  _OnboardingEducationExperienceMockupState createState() => _OnboardingEducationExperienceMockupState();
}

class _OnboardingEducationExperienceMockupState extends State<OnboardingEducationExperienceMockup> {
  int currentStep = 5; // Početni korak za mockup
  final int totalSteps = 6; // Ukupno 6 koraka sada

  // Step 5: Obrazovanje
  String? educationLevel;
  String? fieldOfStudy;
  String? schoolName;
  String? schoolLocation;
  int? educationStartMonth;
  int? educationStartYear;
  int? educationEndMonth;
  int? educationEndYear;

  final Map<String, String> educationLevels = {
    'osnovna': '<PERSON><PERSON><PERSON><PERSON> škola',
    'srednja': 'Sr<PERSON><PERSON> škola',
    'visa': '<PERSON><PERSON><PERSON>',
    'fakultet': 'Fakultet',
    'master': 'Master',
    'doktorat': 'Doktorat',
  };

  // Step 6: Radno iskustvo
  List<WorkExperience> workExperiences = [];
  bool hasNoWorkExperience = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFFAFBFC),
      appBar: AppBar(
        title: Text(
          'Kreiranje profila',
          style: GoogleFonts.inter(
            fontWeight: FontWeight.w600,
            color: const Color(0xFF0F172A),
          ),
        ),
        backgroundColor: Colors.white,
        foregroundColor: const Color(0xFF0F172A),
        elevation: 0,
        surfaceTintColor: Colors.transparent,
        centerTitle: true,
        automaticallyImplyLeading: false,
      ),
      body: Column(
        children: [
          _buildProgressBar(),
          Expanded(
            child: SingleChildScrollView(
              padding: EdgeInsets.all(20),
              child: Container(
                width: double.infinity,
                padding: EdgeInsets.all(24),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(
                    color: const Color(0xFFE2E8F0),
                    width: 1,
                  ),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    if (currentStep == 5) _buildStep5Education(),
                    if (currentStep == 6) _buildStep6WorkExperience(),
                    SizedBox(height: 40),
                    SizedBox(
                      width: double.infinity,
                      height: 56,
                      child: ElevatedButton(
                        onPressed: _canProceed() ? _nextStep : null,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: const Color(0xFF1E3A8A),
                          foregroundColor: Colors.white,
                          disabledBackgroundColor: const Color(0xFFE2E8F0),
                          disabledForegroundColor: const Color(0xFF64748B),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                          elevation: 0,
                        ),
                        child: Text(
                          currentStep == totalSteps ? 'Završi' : 'Nastavi',
                          style: GoogleFonts.inter(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ),
                    SizedBox(height: 16),
                    Center(
                      child: TextButton(
                        onPressed: () {
                          // Skip modal logic
                        },
                        child: Text(
                          'Preskoči kreiranje profila',
                          style: GoogleFonts.inter(
                            fontSize: 16,
                            color: const Color(0xFF64748B),
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProgressBar() {
    return Container(
      padding: EdgeInsets.all(20),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Korak $currentStep od $totalSteps',
                style: GoogleFonts.inter(
                  fontSize: 14,
                  color: const Color(0xFF64748B),
                  fontWeight: FontWeight.w500,
                ),
              ),
              Text(
                '${((currentStep / totalSteps) * 100).round()}%',
                style: GoogleFonts.inter(
                  fontSize: 14,
                  color: const Color(0xFF1E3A8A),
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          SizedBox(height: 12),
          LinearProgressIndicator(
            value: currentStep / totalSteps,
            backgroundColor: const Color(0xFFE2E8F0),
            valueColor: AlwaysStoppedAnimation<Color>(const Color(0xFF1E3A8A)),
            minHeight: 6,
          ),
        ],
      ),
    );
  }

  // ============ STEP 5: OBRAZOVANJE ============
  Widget _buildStep5Education() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Obrazovanje',
          style: GoogleFonts.inter(
            fontSize: 24,
            fontWeight: FontWeight.w700,
            color: const Color(0xFF0F172A),
          ),
        ),
        SizedBox(height: 8),
        Text(
          'Unesite informacije o vašem obrazovanju',
          style: GoogleFonts.inter(
            fontSize: 16,
            color: const Color(0xFF64748B),
          ),
        ),
        SizedBox(height: 32),
        
        // Nivo obrazovanja
        Text(
          'Nivo obrazovanja',
          style: GoogleFonts.inter(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: const Color(0xFF0F172A),
          ),
        ),
        SizedBox(height: 12),
        Container(
          width: double.infinity,
          padding: EdgeInsets.symmetric(horizontal: 16),
          decoration: BoxDecoration(
            border: Border.all(color: const Color(0xFFE2E8F0)),
            borderRadius: BorderRadius.circular(12),
            color: const Color(0xFFFAFBFC),
          ),
          child: DropdownButton<String>(
            value: educationLevel,
            isExpanded: true,
            underline: SizedBox(),
            icon: Icon(Icons.arrow_drop_down, color: const Color(0xFF64748B)),
            hint: Text(
              'Izaberite nivo obrazovanja',
              style: GoogleFonts.inter(
                fontSize: 16,
                color: const Color(0xFF64748B),
              ),
            ),
            items: educationLevels.entries.map((entry) {
              return DropdownMenuItem<String>(
                value: entry.key,
                child: Text(
                  entry.value,
                  style: GoogleFonts.inter(
                    fontSize: 16,
                    color: const Color(0xFF0F172A),
                    fontWeight: FontWeight.w500,
                  ),
                ),
              );
            }).toList(),
            onChanged: (String? newValue) {
              setState(() {
                educationLevel = newValue;
              });
            },
          ),
        ),
        
        SizedBox(height: 24),
        
        // Smjer/Zvanje
        Text(
          'Smjer/Zvanje',
          style: GoogleFonts.inter(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: const Color(0xFF0F172A),
          ),
        ),
        SizedBox(height: 12),
        TextField(
          decoration: InputDecoration(
            hintText: 'Npr. Elektrotehnika, Mašinstvo, Automehanicar...',
            hintStyle: TextStyle(color: const Color(0xFF94A3B8)),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: const Color(0xFFE2E8F0)),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: const Color(0xFFE2E8F0)),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: const Color(0xFF1E3A8A), width: 2),
            ),
            filled: true,
            fillColor: const Color(0xFFFAFBFC),
          ),
        ),
        
        SizedBox(height: 24),
        
        // Naziv škole
        Text(
          'Naziv škole',
          style: GoogleFonts.inter(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: const Color(0xFF0F172A),
          ),
        ),
        SizedBox(height: 12),
        TextField(
          decoration: InputDecoration(
            hintText: 'Unesite naziv škole',
            hintStyle: TextStyle(color: const Color(0xFF94A3B8)),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: const Color(0xFFE2E8F0)),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: const Color(0xFFE2E8F0)),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: const Color(0xFF1E3A8A), width: 2),
            ),
            filled: true,
            fillColor: const Color(0xFFFAFBFC),
          ),
        ),
        
        SizedBox(height: 24),
        
        // Mjesto
        Text(
          'Mjesto',
          style: GoogleFonts.inter(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: const Color(0xFF0F172A),
          ),
        ),
        SizedBox(height: 12),
        TextField(
          decoration: InputDecoration(
            hintText: 'Unesite mjesto gdje ste išli u školu',
            hintStyle: TextStyle(color: const Color(0xFF94A3B8)),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: const Color(0xFFE2E8F0)),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: const Color(0xFFE2E8F0)),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: const Color(0xFF1E3A8A), width: 2),
            ),
            filled: true,
            fillColor: const Color(0xFFFAFBFC),
          ),
        ),
        
        SizedBox(height: 24),
        
        // Datum rođenja - jednostavan picker
        Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Od kada (mjesec/godina)',
                    style: GoogleFonts.inter(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: const Color(0xFF0F172A),
                    ),
                  ),
                  SizedBox(height: 12),
                  _buildMonthYearPicker(
                    'Od',
                    educationStartMonth,
                    educationStartYear,
                    (month, year) {
                      setState(() {
                        educationStartMonth = month;
                        educationStartYear = year;
                      });
                    },
                  ),
                ],
              ),
            ),
            SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Do kada (mjesec/godina)',
                    style: GoogleFonts.inter(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: const Color(0xFF0F172A),
                    ),
                  ),
                  SizedBox(height: 12),
                  _buildMonthYearPicker(
                    'Do',
                    educationEndMonth,
                    educationEndYear,
                    (month, year) {
                      setState(() {
                        educationEndMonth = month;
                        educationEndYear = year;
                      });
                    },
                  ),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }

  // ============ STEP 6: RADNO ISKUSTVO ============
  Widget _buildStep6WorkExperience() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Radno iskustvo',
          style: GoogleFonts.inter(
            fontSize: 24,
            fontWeight: FontWeight.w700,
            color: const Color(0xFF0F172A),
          ),
        ),
        SizedBox(height: 8),
        Text(
          'Dodajte vaše radno iskustvo',
          style: GoogleFonts.inter(
            fontSize: 16,
            color: const Color(0xFF64748B),
          ),
        ),
        SizedBox(height: 24),
        
        // Checkbox za "Nemam radno iskustvo"
        Container(
          padding: EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: const Color(0xFFF8FAFC),
            border: Border.all(color: const Color(0xFFE2E8F0)),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Row(
            children: [
              Checkbox(
                value: hasNoWorkExperience,
                onChanged: (bool? value) {
                  setState(() {
                    hasNoWorkExperience = value ?? false;
                    if (hasNoWorkExperience) {
                      workExperiences.clear();
                    }
                  });
                },
                activeColor: const Color(0xFF1E3A8A),
              ),
              SizedBox(width: 12),
              Expanded(
                child: Text(
                  'Nemam radno iskustvo',
                  style: GoogleFonts.inter(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                    color: const Color(0xFF0F172A),
                  ),
                ),
              ),
            ],
          ),
        ),
        
        if (!hasNoWorkExperience) ...[
          SizedBox(height: 24),
          
          // Lista radnih iskustava
          ...workExperiences.asMap().entries.map((entry) {
            int index = entry.key;
            WorkExperience experience = entry.value;
            return Container(
              margin: EdgeInsets.only(bottom: 16),
              child: _buildWorkExperienceCard(experience, index),
            );
          }).toList(),
          
          // Button za dodavanje novog iskustva
          Container(
            width: double.infinity,
            child: OutlinedButton.icon(
              onPressed: () {
                setState(() {
                  workExperiences.add(WorkExperience());
                });
              },
              icon: Icon(
                Icons.add,
                color: const Color(0xFF1E3A8A),
              ),
              label: Text(
                workExperiences.isEmpty ? 'Dodaj radno iskustvo' : 'Dodaj još jedno iskustvo',
                style: GoogleFonts.inter(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: const Color(0xFF1E3A8A),
                ),
              ),
              style: OutlinedButton.styleFrom(
                padding: EdgeInsets.symmetric(vertical: 16),
                side: BorderSide(color: const Color(0xFF1E3A8A), width: 2),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildWorkExperienceCard(WorkExperience experience, int index) {
    return Container(
      padding: EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border.all(color: const Color(0xFFE2E8F0)),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header sa brojem i delete buttonom
          Row(
            children: [
              Container(
                width: 32,
                height: 32,
                decoration: BoxDecoration(
                  color: const Color(0xFF1E3A8A),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Center(
                  child: Text(
                    '${index + 1}',
                    style: GoogleFonts.inter(
                      fontSize: 14,
                      fontWeight: FontWeight.w700,
                      color: Colors.white,
                    ),
                  ),
                ),
              ),
              SizedBox(width: 12),
              Expanded(
                child: Text(
                  'Radno iskustvo ${index + 1}',
                  style: GoogleFonts.inter(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    color: const Color(0xFF0F172A),
                  ),
                ),
              ),
              IconButton(
                onPressed: () {
                  setState(() {
                    workExperiences.removeAt(index);
                  });
                },
                icon: Icon(
                  Icons.delete_outline,
                  color: const Color(0xFFEF4444),
                ),
              ),
            ],
          ),
          
          SizedBox(height: 20),
          
          // Pozicija
          Text(
            'Pozicija/Radno mjesto',
            style: GoogleFonts.inter(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: const Color(0xFF0F172A),
            ),
          ),
          SizedBox(height: 8),
          TextField(
            onChanged: (value) {
              experience.position = value;
            },
            decoration: InputDecoration(
              hintText: 'Npr. Automehanicar, Elektricar, Vozač...',
              hintStyle: TextStyle(color: const Color(0xFF94A3B8)),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(color: const Color(0xFFE2E8F0)),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(color: const Color(0xFFE2E8F0)),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(color: const Color(0xFF1E3A8A), width: 2),
              ),
              filled: true,
              fillColor: const Color(0xFFFAFBFC),
            ),
          ),
          
          SizedBox(height: 16),
          
          // Kompanija
          Text(
            'Kompanija',
            style: GoogleFonts.inter(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: const Color(0xFF0F172A),
            ),
          ),
          SizedBox(height: 8),
          TextField(
            onChanged: (value) {
              experience.company = value;
            },
            decoration: InputDecoration(
              hintText: 'Unesite naziv kompanije',
              hintStyle: TextStyle(color: const Color(0xFF94A3B8)),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(color: const Color(0xFFE2E8F0)),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(color: const Color(0xFFE2E8F0)),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(color: const Color(0xFF1E3A8A), width: 2),
              ),
              filled: true,
              fillColor: const Color(0xFFFAFBFC),
            ),
          ),
          
          SizedBox(height: 16),
          
          // Mjesto
          Text(
            'Mjesto',
            style: GoogleFonts.inter(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: const Color(0xFF0F172A),
            ),
          ),
          SizedBox(height: 8),
          TextField(
            onChanged: (value) {
              experience.location = value;
            },
            decoration: InputDecoration(
              hintText: 'Unesite mjesto rada',
              hintStyle: TextStyle(color: const Color(0xFF94A3B8)),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(color: const Color(0xFFE2E8F0)),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(color: const Color(0xFFE2E8F0)),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(color: const Color(0xFF1E3A8A), width: 2),
              ),
              filled: true,
              fillColor: const Color(0xFFFAFBFC),
            ),
          ),
          
          SizedBox(height: 20),
          
          // Checkbox za "Trenutno radim ovdje"
          Row(
            children: [
              Checkbox(
                value: experience.isCurrentJob,
                onChanged: (bool? value) {
                  setState(() {
                    experience.isCurrentJob = value ?? false;
                    if (experience.isCurrentJob) {
                      experience.endMonth = null;
                      experience.endYear = null;
                    }
                  });
                },
                activeColor: const Color(0xFF1E3A8A),
              ),
              SizedBox(width: 8),
              Expanded(
                child: Text(
                  'Trenutno radim ovdje',
                  style: GoogleFonts.inter(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: const Color(0xFF0F172A),
                  ),
                ),
              ),
            ],
          ),
          
          SizedBox(height: 16),
          
          // Period rada
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Od kada',
                      style: GoogleFonts.inter(
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                        color: const Color(0xFF0F172A),
                      ),
                    ),
                    SizedBox(height: 8),
                    _buildMonthYearPicker(
                      'Od',
                      experience.startMonth,
                      experience.startYear,
                      (month, year) {
                        setState(() {
                          experience.startMonth = month;
                          experience.startYear = year;
                        });
                      },
                    ),
                  ],
                ),
              ),
              SizedBox(width: 16),
              if (!experience.isCurrentJob)
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Do kada',
                        style: GoogleFonts.inter(
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                          color: const Color(0xFF0F172A),
                        ),
                      ),
                      SizedBox(height: 8),
                      _buildMonthYearPicker(
                        'Do',
                        experience.endMonth,
                        experience.endYear,
                        (month, year) {
                          setState(() {
                            experience.endMonth = month;
                            experience.endYear = year;
                          });
                        },
                      ),
                    ],
                  ),
                ),
              if (experience.isCurrentJob)
                Expanded(
                  child: Container(
                    padding: EdgeInsets.symmetric(vertical: 12, horizontal: 16),
                    decoration: BoxDecoration(
                      color: const Color(0xFFDCFCE7),
                      border: Border.all(color: const Color(0xFF16A34A)),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Text(
                      'Trenutno zapošljen',
                      style: GoogleFonts.inter(
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                        color: const Color(0xFF15803D),
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                ),
            ],
          ),
        ],
      ),
    );
  }

  // Jednostavan month/year picker prilagođen ciljnoj grupi
  Widget _buildMonthYearPicker(
    String label,
    int? selectedMonth,
    int? selectedYear,
    Function(int?, int?) onChanged,
  ) {
    final months = [
      'Jan', 'Feb', 'Mar', 'Apr', 'Maj', 'Jun',
      'Jul', 'Aug', 'Sep', 'Okt', 'Nov', 'Dec'
    ];
    
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        border: Border.all(color: const Color(0xFFE2E8F0)),
        borderRadius: BorderRadius.circular(8),
        color: const Color(0xFFFAFBFC),
      ),
      child: Row(
        children: [
          // Month dropdown
          Expanded(
            flex: 2,
            child: DropdownButton<int>(
              value: selectedMonth,
              isExpanded: true,
              underline: SizedBox(),
              hint: Text(
                'Mjesec',
                style: GoogleFonts.inter(
                  fontSize: 14,
                  color: const Color(0xFF94A3B8),
                ),
              ),
              items: months.asMap().entries.map((entry) {
                return DropdownMenuItem<int>(
                  value: entry.key + 1,
                  child: Text(
                    entry.value,
                    style: GoogleFonts.inter(
                      fontSize: 14,
                      color: const Color(0xFF0F172A),
                    ),
                  ),
                );
              }).toList(),
              onChanged: (int? newMonth) {
                onChanged(newMonth, selectedYear);
              },
            ),
          ),
          
          SizedBox(width: 8),
          
          // Year dropdown
          Expanded(
            flex: 2,
            child: DropdownButton<int>(
              value: selectedYear,
              isExpanded: true,
              underline: SizedBox(),
              hint: Text(
                'Godina',
                style: GoogleFonts.inter(
                  fontSize: 14,
                  color: const Color(0xFF94A3B8),
                ),
              ),
              items: List.generate(50, (index) {
                int year = DateTime.now().year - index;
                return DropdownMenuItem<int>(
                  value: year,
                  child: Text(
                    year.toString(),
                    style: GoogleFonts.inter(
                      fontSize: 14,
                      color: const Color(0xFF0F172A),
                    ),
                  ),
                );
              }),
              onChanged: (int? newYear) {
                onChanged(selectedMonth, newYear);
              },
            ),
          ),
        ],
      ),
    );
  }

  bool _canProceed() {
    if (currentStep == 5) {
      return educationLevel != null && 
             educationStartMonth != null && 
             educationStartYear != null &&
             educationEndMonth != null &&
             educationEndYear != null;
    }
    if (currentStep == 6) {
      if (hasNoWorkExperience) return true;
      return workExperiences.isNotEmpty && 
             workExperiences.every((exp) => 
               exp.position != null && exp.position!.isNotEmpty &&
               exp.company != null && exp.company!.isNotEmpty &&
               exp.startMonth != null && exp.startYear != null &&
               (exp.isCurrentJob || (exp.endMonth != null && exp.endYear != null))
             );
    }
    return false;
  }

  void _nextStep() {
    if (currentStep < totalSteps) {
      setState(() {
        currentStep++;
      });
    } else {
      // Complete onboarding
      print('Onboarding completed!');
    }
  }
}

// Model za radno iskustvo
class WorkExperience {
  String? position;
  String? company;
  String? location;
  int? startMonth;
  int? startYear;
  int? endMonth;
  int? endYear;
  bool isCurrentJob = false;

  WorkExperience({
    this.position,
    this.company,
    this.location,
    this.startMonth,
    this.startYear,
    this.endMonth,
    this.endYear,
    this.isCurrentJob = false,
  });
}