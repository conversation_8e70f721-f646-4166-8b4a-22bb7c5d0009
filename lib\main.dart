import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'screens/splash_screen.dart';
import 'screens/edit_personal_info_screen.dart';
import 'screens/edit_location_screen.dart';
import 'screens/edit_driving_license_screen.dart';
import 'screens/edit_languages_screen.dart';
import 'screens/edit_education_screen.dart';
import 'screens/edit_work_experience_screen.dart';

void main() {
  runApp(MyApp());
}

class MyApp extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Job Board',
      debugShowCheckedModeBanner: false,
      theme: ThemeData(
        useMaterial3: true,
        colorScheme: ColorScheme.fromSeed(
          seedColor: const Color(0xFF1E3A8A), // Professional dark blue
          brightness: Brightness.light,
        ).copyWith(
          primary: const Color(0xFF1E3A8A),
          secondary: const Color(0xFF3B82F6),
          surface: Colors.white,
          background: const Color(0xFFFAFBFC),
          onPrimary: Colors.white,
          onSecondary: Colors.white,
          onSurface: const Color(0xFF0F172A),
          onBackground: const Color(0xFF0F172A),
        ),
        fontFamily: GoogleFonts.inter().fontFamily,
        cardTheme: CardThemeData(
          elevation: 0,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
            side: BorderSide(
              color: const Color(0xFFE2E8F0),
              width: 1,
            ),
          ),
          color: Colors.white,
          shadowColor: Colors.transparent,
        ),
        appBarTheme: AppBarTheme(
          elevation: 0,
          backgroundColor: Colors.white,
          foregroundColor: const Color(0xFF0A0A0A), // hsl(0 0% 10%)
          surfaceTintColor: Colors.transparent,
        ),
        scaffoldBackgroundColor: const Color(0xFFFAFBFC),
        bottomNavigationBarTheme: BottomNavigationBarThemeData(
          selectedItemColor: const Color(0xFF1E3A8A),
          unselectedItemColor: const Color(0xFF64748B), // hsl(215.4 16.3% 46.9%)
          type: BottomNavigationBarType.fixed,
          elevation: 8,
          selectedLabelStyle: TextStyle(fontWeight: FontWeight.w600),
        ),
        textTheme: TextTheme(
          headlineSmall: TextStyle(
            color: const Color(0xFF0A0A0A),
            fontWeight: FontWeight.bold,
          ),
          bodyLarge: TextStyle(
            color: const Color(0xFF0A0A0A),
          ),
          bodyMedium: TextStyle(
            color: const Color(0xFF64748B),
          ),
        ),
        inputDecorationTheme: InputDecorationTheme(
          filled: true,
          fillColor: const Color(0xFFF1F5F9), // hsl(220 40% 95%)
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide(color: const Color(0xFFE2E8F0)), // hsl(220 30% 90%)
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide(color: const Color(0xFFE2E8F0)),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide(color: const Color(0xFF1E3A8A), width: 2),
          ),
        ),
        visualDensity: VisualDensity.adaptivePlatformDensity,
      ),
      routes: {
        '/edit-personal-info': (context) => EditPersonalInfoScreen(),
        '/edit-location': (context) => EditLocationScreen(),
        '/edit-driving-license': (context) => EditDrivingLicenseScreen(),
        '/edit-languages': (context) => EditLanguagesScreen(),
        '/edit-education': (context) => EditEducationScreen(),
        '/edit-work-experience': (context) => EditWorkExperienceScreen(),
      },
      home: SplashScreen(),
    );
  }
}