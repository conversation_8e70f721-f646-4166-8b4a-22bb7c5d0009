import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:image_picker/image_picker.dart';
import 'dart:io';
import 'onboarding_screen.dart';
import 'profile_screen_complete.dart';
import '../constants/design_constants.dart';

class _StepData {
  final String title;
  final String subtitle;
  final bool completed;
  final IconData icon;
  final List<String> data;

  _StepData({
    required this.title,
    required this.subtitle,
    required this.completed,
    required this.icon,
    required this.data,
  });
}

class ProfileScreenNew extends StatefulWidget {
  @override
  _ProfileScreenNewState createState() => _ProfileScreenNewState();
}

class _ProfileScreenNewState extends State<ProfileScreenNew> {
  // Dirty state tracking
  bool _hasChanges = false;
  
  // Profile image
  File? _profileImage;
  final ImagePicker _picker = ImagePicker();
  
  // Mock data - user completed 5/6 onboarding steps
  final Map<String, dynamic> _mockUserData = {
    'firstName': 'Marko',
    'lastName': '<PERSON>rov<PERSON>',
    'phoneNumber': '+387 61 123 456',
    'country': 'BA',
    'location': 'Sarajevo',
    'drivingLicenses': ['B', 'C'],
    'germanLevel': 'b1',
    'englishLevel': 'a2',
    'education': {
      'level': 'srednja',
      'fieldOfStudy': 'Automehanicar',
      'schoolName': 'Tehnička škola',
      'location': 'Sarajevo',
      'startMonth': 9,
      'startYear': 2015,
      'endMonth': 6,
      'endYear': 2019,
    },
    // No work experience yet - step 6 not completed
  };

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFFAFBFC),
      body: Stack(
        children: [
          SingleChildScrollView(
            padding: EdgeInsets.fromLTRB(20, 20, 20, 100), // Bottom padding for floating button
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildProfileHeader(),
                SizedBox(height: 24),
                _buildImageUploadSection(),
                SizedBox(height: 24),
                _buildProgressAccordion(),
                
                // Development button
                SizedBox(height: DesignConstants.spaceXXL),
                Container(
                  width: double.infinity,
                  decoration: BoxDecoration(
                    border: Border.all(
                      color: DesignConstants.modernErrorRed,
                      width: 2,
                    ),
                    borderRadius: BorderRadius.circular(DesignConstants.radiusLarge),
                    color: DesignConstants.modernErrorRed.withOpacity(0.05),
                  ),
                  child: TextButton.icon(
                    onPressed: _navigateToCompleteProfile,
                    icon: Icon(
                      Icons.visibility,
                      color: DesignConstants.modernErrorRed,
                      size: 18,
                    ),
                    label: Text(
                      'DEV: Pogledaj kompletiran profil (100%)',
                      style: DesignConstants.labelMedium.copyWith(
                        color: DesignConstants.modernErrorRed,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    style: TextButton.styleFrom(
                      padding: EdgeInsets.symmetric(vertical: DesignConstants.spaceM),
                    ),
                  ),
                ),
              ],
            ),
          ),
          
          // Floating Save Button
          if (_hasChanges)
            Positioned(
              left: 20,
              right: 20,
              bottom: 20,
              child: _buildFloatingSaveButton(),
            ),
        ],
      ),
    );
  }

  Widget _buildProfileHeader() {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(DesignConstants.spaceXL),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            DesignConstants.white,
            DesignConstants.modernWarningYellow.withOpacity(0.01),
          ],
        ),
        borderRadius: BorderRadius.circular(DesignConstants.radiusXLarge),
        border: Border.all(
          color: DesignConstants.modernWarningYellow.withOpacity(0.2),
          width: 1.5,
        ),
        boxShadow: [
          BoxShadow(
            color: DesignConstants.modernWarningYellow.withOpacity(0.1),
            blurRadius: 12,
            offset: Offset(0, 4),
            spreadRadius: 1,
          ),
          BoxShadow(
            color: Colors.black.withOpacity(0.03),
            blurRadius: 8,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: EdgeInsets.all(DesignConstants.spaceS),
                decoration: BoxDecoration(
                  color: DesignConstants.modernWarningYellow.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(DesignConstants.radiusMedium),
                ),
                child: Icon(
                  Icons.account_circle_outlined,
                  color: DesignConstants.modernWarningYellow,
                  size: DesignConstants.iconMedium,
                ),
              ),
              SizedBox(width: DesignConstants.spaceM),
              Expanded(
                child: Text(
                  'Dopunite vaš profil',
                  style: DesignConstants.titleMedium.copyWith(
                    color: DesignConstants.black,
                  ),
                ),
              ),
              Container(
                padding: EdgeInsets.symmetric(
                  horizontal: DesignConstants.spaceM,
                  vertical: DesignConstants.spaceS,
                ),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      DesignConstants.modernWarningYellow,
                      DesignConstants.modernWarningYellow.withOpacity(0.8),
                    ],
                  ),
                  borderRadius: BorderRadius.circular(DesignConstants.radiusXLarge),
                  boxShadow: [
                    BoxShadow(
                      color: DesignConstants.modernWarningYellow.withOpacity(0.3),
                      blurRadius: 8,
                      offset: Offset(0, 2),
                    ),
                  ],
                ),
                child: Text(
                  '83%',
                  style: DesignConstants.labelMedium.copyWith(
                    fontWeight: FontWeight.w700,
                    color: DesignConstants.white,
                  ),
                ),
              ),
            ],
          ),
          SizedBox(height: DesignConstants.spaceL),
          ClipRRect(
            borderRadius: BorderRadius.circular(DesignConstants.radiusSmall),
            child: LinearProgressIndicator(
              value: 0.83, // 5/6 steps completed
              backgroundColor: DesignConstants.modernWarningYellow.withOpacity(0.1),
              valueColor: AlwaysStoppedAnimation<Color>(DesignConstants.modernWarningYellow),
              minHeight: 8,
            ),
          ),
          SizedBox(height: DesignConstants.spaceM),
          Text(
            'Samo još jedan korak do potpunog profila! Dodajte vaše radno iskustvo da privučete više pažnje poslodavaca.',
            style: DesignConstants.bodyMedium.copyWith(
              color: DesignConstants.darkGray,
              height: 1.4,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildImageUploadSection() {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: const Color(0xFFE2E8F0),
          width: 1,
        ),
      ),
      child: Column(
        children: [
          Row(
            children: [
              // Profile Image Circle
              GestureDetector(
                onTap: _pickImage,
                child: Container(
                  width: 80,
                  height: 80,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: const Color(0xFFF8FAFC),
                    border: Border.all(
                      color: const Color(0xFFE2E8F0),
                      width: 2,
                    ),
                  ),
                  child: _profileImage != null
                      ? ClipOval(
                          child: Image.file(
                            _profileImage!,
                            fit: BoxFit.cover,
                          ),
                        )
                      : Icon(
                          Icons.add_a_photo_outlined,
                          size: 32,
                          color: const Color(0xFF64748B),
                        ),
                ),
              ),
              
              SizedBox(width: 16),
              
              // Upload info and button
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Dodajte profilnu sliku',
                      style: GoogleFonts.inter(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: const Color(0xFF0F172A),
                      ),
                    ),
                    SizedBox(height: 4),
                    Text(
                      'Poslodavci su 3x vjerjjatniji da kontaktiraju kandidate sa slikom',
                      style: GoogleFonts.inter(
                        fontSize: 13,
                        color: const Color(0xFF64748B),
                        fontWeight: FontWeight.w400,
                      ),
                    ),
                    SizedBox(height: 12),
                    OutlinedButton.icon(
                      onPressed: _pickImage,
                      icon: Icon(
                        Icons.upload_rounded,
                        size: 16,
                        color: const Color(0xFF1E3A8A),
                      ),
                      label: Text(
                        'Dodaj sliku',
                        style: GoogleFonts.inter(
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                          color: const Color(0xFF1E3A8A),
                        ),
                      ),
                      style: OutlinedButton.styleFrom(
                        side: BorderSide(color: const Color(0xFF1E3A8A)),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                        padding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildProgressAccordion() {
    final steps = [
      _StepData(
        title: 'Osobni podaci',
        subtitle: 'Ime, prezime, telefon',
        completed: true,
        icon: Icons.person_outline,
        data: [
          'Ime: ${_mockUserData['firstName']}',
          'Prezime: ${_mockUserData['lastName']}',
          'Telefon: ${_mockUserData['phoneNumber']}',
        ]
      ),
      _StepData(
        title: 'Lokacija',
        subtitle: 'Država i mjesto',
        completed: true,
        icon: Icons.location_on_outlined,
        data: [
          'Država: Bosna i Hercegovina',
          'Mjesto: ${_mockUserData['location']}',
        ]
      ),
      _StepData(
        title: 'Vozačka dozvola',
        subtitle: 'Kategorije vozačke dozvole',
        completed: true,
        icon: Icons.drive_eta_outlined,
        data: [
          'Kategorije: ${_mockUserData['drivingLicenses'].join(', ')}',
        ]
      ),
      _StepData(
        title: 'Jezičko znanje',
        subtitle: 'Njemački i engleski jezik',
        completed: true,
        icon: Icons.language_outlined,
        data: [
          'Njemački: B1 - Srednje znanje',
          'Engleski: A2 - Osnovno znanje',
        ]
      ),
      _StepData(
        title: 'Obrazovanje',
        subtitle: 'Škola i kvalifikacije',
        completed: true,
        icon: Icons.school_outlined,
        data: [
          'Nivo: Srednja škola',
          'Smjer: ${_mockUserData['education']['fieldOfStudy']}',
          'Škola: ${_mockUserData['education']['schoolName']}',
          'Period: 09/2015 - 06/2019',
        ]
      ),
      _StepData(
        title: 'Radno iskustvo',
        subtitle: 'Vaša radna pozicija i kompanije',
        completed: false,
        icon: Icons.work_outline,
        data: []
      ),
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Pregled profila',
          style: DesignConstants.titleMedium.copyWith(
            color: DesignConstants.black,
          ),
        ),
        SizedBox(height: DesignConstants.spaceM),
        
        ...steps.asMap().entries.map((entry) {
          int index = entry.key;
          _StepData step = entry.value;
          
          return Container(
            margin: EdgeInsets.only(bottom: 12),
            child: _buildAccordionItem(
              step.title,
              step.subtitle,
              step.completed,
              step.icon,
              step.data,
              index + 1,
            ),
          );
        }).toList(),
      ],
    );
  }

  Widget _buildAccordionItem(
    String title,
    String subtitle,
    bool completed,
    IconData icon,
    List<String> data,
    int stepNumber,
  ) {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            DesignConstants.white,
            DesignConstants.modernPrimaryBlue.withOpacity(0.01),
          ],
        ),
        borderRadius: BorderRadius.circular(DesignConstants.radiusLarge),
        border: Border.all(
          color: DesignConstants.modernPrimaryBlue.withOpacity(0.1),
          width: 1.5,
        ),
        boxShadow: [
          BoxShadow(
            color: DesignConstants.modernPrimaryBlue.withOpacity(0.05),
            blurRadius: 8,
            offset: Offset(0, 2),
          ),
          BoxShadow(
            color: Colors.black.withOpacity(0.03),
            blurRadius: 6,
            offset: Offset(0, 1),
          ),
        ],
      ),
      child: ExpansionTile(
        tilePadding: EdgeInsets.symmetric(horizontal: 16, vertical: 4),
        childrenPadding: EdgeInsets.fromLTRB(16, 0, 16, 16),
        leading: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Step number
            Container(
              width: 24,
              height: 24,
              decoration: BoxDecoration(
                color: completed ? DesignConstants.modernSuccessGreen : DesignConstants.lightGray,
                borderRadius: BorderRadius.circular(6),
                boxShadow: completed ? [
                  BoxShadow(
                    color: DesignConstants.modernSuccessGreen.withOpacity(0.3),
                    blurRadius: 4,
                    offset: Offset(0, 2),
                  ),
                ] : null,
              ),
              child: Center(
                child: completed
                    ? Icon(
                        Icons.check,
                        size: 14,
                        color: Colors.white,
                      )
                    : Text(
                        stepNumber.toString(),
                        style: DesignConstants.labelSmall.copyWith(
                          color: DesignConstants.darkGray,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
              ),
            ),
            SizedBox(width: DesignConstants.spaceM),
            // Icon
            Container(
              padding: EdgeInsets.all(6),
              decoration: BoxDecoration(
                color: completed 
                    ? DesignConstants.modernSuccessGreen.withOpacity(0.1)
                    : DesignConstants.lightGray.withOpacity(0.5),
                borderRadius: BorderRadius.circular(6),
              ),
              child: Icon(
                icon,
                size: 16,
                color: completed 
                    ? DesignConstants.modernSuccessGreen
                    : DesignConstants.darkGray,
              ),
            ),
          ],
        ),
        title: Text(
          title,
          style: DesignConstants.titleSmall.copyWith(
            color: DesignConstants.black,
          ),
        ),
        subtitle: Text(
          subtitle,
          style: DesignConstants.bodySmall.copyWith(
            color: DesignConstants.darkGray,
          ),
        ),
        trailing: !completed
            ? Container(
                padding: EdgeInsets.symmetric(
                  horizontal: DesignConstants.spaceS,
                  vertical: DesignConstants.spaceXS,
                ),
                decoration: BoxDecoration(
                  color: DesignConstants.modernWarningYellow.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(DesignConstants.radiusMedium),
                ),
                child: Text(
                  'Dodaj',
                  style: DesignConstants.labelSmall.copyWith(
                    color: DesignConstants.modernWarningYellow,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              )
            : IconButton(
                onPressed: () => _editSection(title),
                icon: Icon(
                  Icons.edit_outlined,
                  size: 18,
                  color: DesignConstants.modernPrimaryBlue,
                ),
                padding: EdgeInsets.all(4),
                constraints: BoxConstraints(minWidth: 32, minHeight: 32),
              ),
        children: [
          if (completed && data.isNotEmpty)
            Container(
              width: double.infinity,
              padding: EdgeInsets.all(DesignConstants.spaceM),
              decoration: BoxDecoration(
                color: DesignConstants.modernSuccessGreen.withOpacity(0.1),
                borderRadius: BorderRadius.circular(DesignConstants.radiusMedium),
                border: Border.all(
                  color: DesignConstants.modernSuccessGreen.withOpacity(0.2),
                  width: 1,
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: data.map((item) {
                  return Padding(
                    padding: EdgeInsets.only(bottom: 6),
                    child: Row(
                      children: [
                        Container(
                          width: 4,
                          height: 4,
                          decoration: BoxDecoration(
                            color: DesignConstants.modernSuccessGreen,
                            shape: BoxShape.circle,
                          ),
                        ),
                        SizedBox(width: DesignConstants.spaceS),
                        Expanded(
                          child: Text(
                            item,
                            style: DesignConstants.bodySmall.copyWith(
                              color: DesignConstants.modernSuccessGreen,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                      ],
                    ),
                  );
                }).toList(),
              ),
            )
          else if (!completed)
            Container(
              width: double.infinity,
              padding: EdgeInsets.all(DesignConstants.spaceM),
              decoration: BoxDecoration(
                color: DesignConstants.modernWarningYellow.withOpacity(0.1),
                borderRadius: BorderRadius.circular(DesignConstants.radiusMedium),
                border: Border.all(
                  color: DesignConstants.modernWarningYellow.withOpacity(0.2),
                  width: 1,
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.info_outline,
                    size: 16,
                    color: DesignConstants.modernWarningYellow,
                  ),
                  SizedBox(width: DesignConstants.spaceS),
                  Expanded(
                    child: Text(
                      'Ovaj korak još nije završen. Dodajte vaše radno iskustvo da završite profil.',
                      style: DesignConstants.bodySmall.copyWith(
                        color: DesignConstants.modernWarningYellow,
                      ),
                    ),
                  ),
                  SizedBox(width: DesignConstants.spaceS),
                  Container(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(DesignConstants.radiusMedium),
                      boxShadow: [
                        BoxShadow(
                          color: DesignConstants.modernWarningYellow.withOpacity(0.3),
                          blurRadius: 6,
                          offset: Offset(0, 2),
                        ),
                      ],
                    ),
                    child: ElevatedButton.icon(
                      onPressed: () => _navigateToWorkExperience(),
                      icon: Icon(
                        Icons.add,
                        size: 16,
                        color: DesignConstants.white,
                      ),
                      label: Text(
                        'Dodaj sada',
                        style: DesignConstants.buttonMedium,
                      ),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: DesignConstants.modernWarningYellow,
                        foregroundColor: DesignConstants.white,
                        elevation: 0,
                        shadowColor: Colors.transparent,
                        padding: EdgeInsets.symmetric(
                          horizontal: DesignConstants.spaceM,
                          vertical: DesignConstants.spaceS,
                        ),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(DesignConstants.radiusMedium),
                        ),
                        minimumSize: Size(0, 32),
                      ),
                    ),
                  ),
                ],
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildFloatingSaveButton() {
    return Container(
      width: double.infinity,
      height: 56,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(DesignConstants.radiusLarge),
        boxShadow: DesignConstants.modernBrandButtonShadow,
      ),
      child: ElevatedButton.icon(
        onPressed: _saveChanges,
        icon: Icon(
          Icons.save_outlined,
          size: DesignConstants.iconMedium,
          color: DesignConstants.white,
        ),
        label: Text(
          'Spremi promjene',
          style: DesignConstants.buttonLarge,
        ),
        style: ElevatedButton.styleFrom(
          backgroundColor: DesignConstants.modernPrimaryBlue,
          foregroundColor: DesignConstants.white,
          elevation: 0,
          shadowColor: Colors.transparent,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(DesignConstants.radiusLarge),
          ),
          padding: EdgeInsets.symmetric(vertical: DesignConstants.spaceM),
        ),
      ),
    );
  }

  Future<void> _pickImage() async {
    try {
      final XFile? image = await _picker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 800,
        maxHeight: 800,
        imageQuality: 85,
      );
      
      if (image != null) {
        setState(() {
          _profileImage = File(image.path);
          _hasChanges = true;
        });
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Greška pri učitavanju slike'),
          backgroundColor: DesignConstants.modernErrorRed,
        ),
      );
    }
  }

  void _saveChanges() {
    // Save logic here
    setState(() {
      _hasChanges = false;
    });
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(Icons.check_circle, color: DesignConstants.white),
            SizedBox(width: DesignConstants.spaceS),
            Text('Profil uspješno ažuriran!'),
          ],
        ),
        backgroundColor: DesignConstants.modernSuccessGreen,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(DesignConstants.radiusMedium),
        ),
      ),
    );
  }

  Future<void> _navigateToWorkExperience() async {
    final result = await Navigator.pushNamed(context, '/edit-work-experience');
    
    // If user completed work experience, refresh the profile
    if (result == true) {
      setState(() {
        // Refresh profile data - in real app this would fetch from API
        // For now we'll just show a success message
      });
      
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              Icon(Icons.check_circle, color: DesignConstants.white),
              SizedBox(width: DesignConstants.spaceS),
              Text('Radno iskustvo uspješno dodano!'),
            ],
          ),
          backgroundColor: DesignConstants.modernSuccessGreen,
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(DesignConstants.radiusMedium),
          ),
        ),
      );
    }
  }

  void _editSection(String sectionTitle) {
    String routeName = '';
    
    switch (sectionTitle) {
      case 'Osobni podaci':
        routeName = '/edit-personal-info';
        break;
      case 'Lokacija':
        routeName = '/edit-location';
        break;
      case 'Vozačka dozvola':
        routeName = '/edit-driving-license';
        break;
      case 'Jezičko znanje':
        routeName = '/edit-languages';
        break;
      case 'Obrazovanje':
        routeName = '/edit-education';
        break;
      case 'Radno iskustvo':
        routeName = '/edit-work-experience';
        break;
    }
    
    if (routeName.isNotEmpty) {
      Navigator.pushNamed(context, routeName);
    }
  }

  void _navigateToCompleteProfile() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ProfileScreenComplete(),
      ),
    );
  }
}