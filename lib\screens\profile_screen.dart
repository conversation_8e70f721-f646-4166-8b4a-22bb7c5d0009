import 'package:flutter/material.dart';

class ProfileScreen extends StatefulWidget {
  @override
  _ProfileScreenState createState() => _ProfileScreenState();
}

class _ProfileScreenState extends State<ProfileScreen> {
  final _formKey = GlobalKey<FormState>();
  
  String _ime = '';
  String _prezime = '';
  String _telefon = '';
  String _email = '';
  String _drzava = 'Hrvatska';
  String _nemacki = 'Bez znanja';
  String _engleski = 'Bez znanja';
  List<String> _vozacke = [];
  List<Map<String, String>> _obrazovanje = [];
  List<Map<String, String>> _iskustvo = [];

  final List<String> _jezici = ['Bez znanja', 'A1', 'A2', 'B1', 'B2', 'C1', 'C2'];
  final List<String> _vozackeKategorije = ['A', 'B', 'C', 'D', 'E'];
  final List<String> _drzave = ['Hrvatska', '<PERSON><PERSON><PERSON><PERSON><PERSON>', 'Aust<PERSON><PERSON>', '<PERSON>lovenija', 'Bosna i Hercegovina', '<PERSON><PERSON><PERSON>'];

  @override
  Widget build(BuildContext context) {
    return Material(
      color: const Color(0xFFFAFBFC),
      child: Column(
        children: [
          // Save button header
          Container(
            color: Colors.white,
            child: Container(
              padding: EdgeInsets.fromLTRB(20, 12, 20, 12),
              child: Row(
                children: [
                  Spacer(),
                  ElevatedButton.icon(
                    onPressed: _saveProfile,
                    icon: Icon(Icons.save_outlined, size: 18),
                    label: Text('Sačuvaj'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFF1E3A8A),
                      foregroundColor: Colors.white,
                      elevation: 0,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      padding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                    ),
                  ),
                ],
              ),
            ),
          ),
          Expanded(
            child: Form(
              key: _formKey,
              child: SingleChildScrollView(
                padding: EdgeInsets.all(20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
              // Profile Completion Header
              Container(
                width: double.infinity,
                padding: EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(
                    color: const Color(0xFFE2E8F0),
                    width: 1,
                  ),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Container(
                          padding: EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: const Color(0xFF1E3A8A).withOpacity(0.1),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Icon(
                            Icons.account_circle_outlined,
                            color: const Color(0xFF1E3A8A),
                            size: 20,
                          ),
                        ),
                        SizedBox(width: 12),
                        Expanded(
                          child: Text(
                            'Dopunite vaš profil',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.w600,
                              color: const Color(0xFF0F172A),
                            ),
                          ),
                        ),
                        Text(
                          '47%',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                            color: const Color(0xFFF59E0B),
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: 16),
                    ClipRRect(
                      borderRadius: BorderRadius.circular(8),
                      child: LinearProgressIndicator(
                        value: 0.47,
                        backgroundColor: const Color(0xFFE2E8F0),
                        valueColor: AlwaysStoppedAnimation<Color>(const Color(0xFFF59E0B)),
                        minHeight: 8,
                      ),
                    ),
                    SizedBox(height: 12),
                    Text(
                      'Dodajte još nekoliko informacija da bi vaš profil bio potpuniji i privukao više pažnje poslodavaca.',
                      style: TextStyle(
                        fontSize: 14,
                        color: const Color(0xFF64748B),
                        fontWeight: FontWeight.w400,
                      ),
                    ),
                  ],
                ),
              ),
              
              SizedBox(height: 24),

              _buildSection('Osnovni podaci', [
                _buildTextField('Ime', (value) => _ime = value),
                _buildTextField('Prezime', (value) => _prezime = value),
                _buildTextField('Telefon', (value) => _telefon = value),
                _buildTextField('Email', (value) => _email = value, keyboardType: TextInputType.emailAddress),
                _buildDropdown('Država', _drzava, _drzave, (value) => setState(() => _drzava = value!)),
              ]),
              _buildSection('Jezici', [
                _buildDropdown('Njemački jezik', _nemacki, _jezici, (value) => setState(() => _nemacki = value!)),
                _buildDropdown('Engleski jezik', _engleski, _jezici, (value) => setState(() => _engleski = value!)),
              ]),
              _buildSection('Vozačka dozvola', [
                _buildMultiSelect(),
              ]),
              _buildSection('Obrazovanje', [
                _buildEducationList(),
                _buildAddButton('Dodaj obrazovanje', _addEducation),
              ]),
              _buildSection('Radno iskustvo', [
                _buildExperienceList(),
                _buildAddButton('Dodaj iskustvo', _addExperience),
              ]),
              
              SizedBox(height: 40),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSection(String title, List<Widget> children) {
    return Container(
      width: double.infinity,
      margin: EdgeInsets.only(bottom: 20),
      padding: EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: const Color(0xFFE2E8F0),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: const Color(0xFF0F172A),
            ),
          ),
          SizedBox(height: 16),
          ...children,
        ],
      ),
    );
  }

  Widget _buildTextField(String label, Function(String) onChanged, {TextInputType? keyboardType}) {
    return Padding(
      padding: EdgeInsets.only(bottom: 16),
      child: TextFormField(
        decoration: InputDecoration(
          labelText: label,
          labelStyle: TextStyle(
            color: const Color(0xFF64748B),
            fontWeight: FontWeight.w500,
          ),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide(color: const Color(0xFFE2E8F0)),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide(color: const Color(0xFFE2E8F0)),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide(color: const Color(0xFF1E3A8A), width: 2),
          ),
          filled: true,
          fillColor: const Color(0xFFFAFBFC),
        ),
        keyboardType: keyboardType,
        onChanged: onChanged,
        validator: (value) {
          if (value?.isEmpty ?? true) {
            return 'Ovo polje je obavezno';
          }
          return null;
        },
      ),
    );
  }

  Widget _buildDropdown(String label, String value, List<String> items, Function(String?) onChanged) {
    return Padding(
      padding: EdgeInsets.only(bottom: 16),
      child: DropdownButtonFormField<String>(
        decoration: InputDecoration(
          labelText: label,
          labelStyle: TextStyle(
            color: const Color(0xFF64748B),
            fontWeight: FontWeight.w500,
          ),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide(color: const Color(0xFFE2E8F0)),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide(color: const Color(0xFFE2E8F0)),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide(color: const Color(0xFF1E3A8A), width: 2),
          ),
          filled: true,
          fillColor: const Color(0xFFFAFBFC),
        ),
        value: value,
        items: items.map((String item) {
          return DropdownMenuItem<String>(
            value: item,
            child: Text(
              item,
              style: TextStyle(
                color: const Color(0xFF0F172A),
                fontWeight: FontWeight.w500,
              ),
            ),
          );
        }).toList(),
        onChanged: onChanged,
      ),
    );
  }

  Widget _buildMultiSelect() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Kategorije:',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: const Color(0xFF64748B),
          ),
        ),
        SizedBox(height: 12),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: _vozackeKategorije.map((kategorija) {
            final isSelected = _vozacke.contains(kategorija);
            return GestureDetector(
              onTap: () {
                setState(() {
                  if (isSelected) {
                    _vozacke.remove(kategorija);
                  } else {
                    _vozacke.add(kategorija);
                  }
                });
              },
              child: Container(
                padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                decoration: BoxDecoration(
                  color: isSelected 
                      ? const Color(0xFF1E3A8A) 
                      : const Color(0xFFFAFBFC),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: isSelected 
                        ? const Color(0xFF1E3A8A) 
                        : const Color(0xFFE2E8F0),
                  ),
                ),
                child: Text(
                  kategorija,
                  style: TextStyle(
                    color: isSelected 
                        ? Colors.white 
                        : const Color(0xFF0F172A),
                    fontWeight: FontWeight.w500,
                    fontSize: 14,
                  ),
                ),
              ),
            );
          }).toList(),
        ),
        SizedBox(height: 16),
      ],
    );
  }

  Widget _buildEducationList() {
    return Column(
      children: _obrazovanje.map((edu) {
        int index = _obrazovanje.indexOf(edu);
        return Card(
          child: ListTile(
            title: Text(edu['zvanje'] ?? ''),
            subtitle: Text('${edu['od']} - ${edu['do']}'),
            trailing: IconButton(
              icon: Icon(Icons.delete, color: Colors.red),
              onPressed: () => _removeEducation(index),
            ),
          ),
        );
      }).toList(),
    );
  }

  Widget _buildExperienceList() {
    return Column(
      children: _iskustvo.map((exp) {
        int index = _iskustvo.indexOf(exp);
        return Card(
          child: ListTile(
            title: Text(exp['pozicija'] ?? ''),
            subtitle: Text('${exp['od']} - ${exp['do']}\n${exp['mjesto']}, ${exp['drzava']}'),
            trailing: IconButton(
              icon: Icon(Icons.delete, color: Colors.red),
              onPressed: () => _removeExperience(index),
            ),
          ),
        );
      }).toList(),
    );
  }

  Widget _buildAddButton(String text, VoidCallback onPressed) {
    return Container(
      width: double.infinity,
      margin: EdgeInsets.only(top: 16),
      child: ElevatedButton.icon(
        onPressed: onPressed,
        icon: Icon(Icons.add_rounded, size: 18),
        label: Text(text),
        style: ElevatedButton.styleFrom(
          backgroundColor: const Color(0xFF1E3A8A),
          foregroundColor: Colors.white,
          elevation: 0,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          padding: EdgeInsets.symmetric(horizontal: 20, vertical: 12),
        ),
      ),
    );
  }

  void _addEducation() {
    showDialog(
      context: context,
      builder: (context) => _EducationDialog(
        onSave: (education) {
          setState(() {
            _obrazovanje.add(education);
          });
        },
      ),
    );
  }

  void _addExperience() {
    showDialog(
      context: context,
      builder: (context) => _ExperienceDialog(
        onSave: (experience) {
          setState(() {
            _iskustvo.add(experience);
          });
        },
      ),
    );
  }

  void _removeEducation(int index) {
    setState(() {
      _obrazovanje.removeAt(index);
    });
  }

  void _removeExperience(int index) {
    setState(() {
      _iskustvo.removeAt(index);
    });
  }

  void _saveProfile() {
    if (_formKey.currentState!.validate()) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Profil sačuvan!')),
      );
    }
  }
}

class _EducationDialog extends StatefulWidget {
  final Function(Map<String, String>) onSave;

  _EducationDialog({required this.onSave});

  @override
  _EducationDialogState createState() => _EducationDialogState();
}

class _EducationDialogState extends State<_EducationDialog> {
  final _formKey = GlobalKey<FormState>();
  String _od = '';
  String _do = '';
  String _zvanje = '';

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text('Dodaj obrazovanje'),
      content: Form(
        key: _formKey,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextFormField(
              decoration: InputDecoration(labelText: 'Od (MM/YYYY)'),
              onChanged: (value) => _od = value,
              validator: (value) => value?.isEmpty ?? true ? 'Obavezno polje' : null,
            ),
            TextFormField(
              decoration: InputDecoration(labelText: 'Do (MM/YYYY)'),
              onChanged: (value) => _do = value,
              validator: (value) => value?.isEmpty ?? true ? 'Obavezno polje' : null,
            ),
            TextFormField(
              decoration: InputDecoration(labelText: 'Stečeno zvanje'),
              onChanged: (value) => _zvanje = value,
              validator: (value) => value?.isEmpty ?? true ? 'Obavezno polje' : null,
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: Text('Odustani'),
        ),
        ElevatedButton(
          onPressed: () {
            if (_formKey.currentState!.validate()) {
              widget.onSave({
                'od': _od,
                'do': _do,
                'zvanje': _zvanje,
              });
              Navigator.pop(context);
            }
          },
          child: Text('Sačuvaj'),
        ),
      ],
    );
  }
}

class _ExperienceDialog extends StatefulWidget {
  final Function(Map<String, String>) onSave;

  _ExperienceDialog({required this.onSave});

  @override
  _ExperienceDialogState createState() => _ExperienceDialogState();
}

class _ExperienceDialogState extends State<_ExperienceDialog> {
  final _formKey = GlobalKey<FormState>();
  String _od = '';
  String _do = '';
  String _pozicija = '';
  String _mjesto = '';
  String _drzava = 'Hrvatska';

  final List<String> _drzave = ['Hrvatska', 'Njemačka', 'Austrija', 'Slovenija', 'Bosna i Hercegovina', 'Srbija'];

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text('Dodaj radno iskustvo'),
      content: Form(
        key: _formKey,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextFormField(
              decoration: InputDecoration(labelText: 'Od (MM/YYYY)'),
              onChanged: (value) => _od = value,
              validator: (value) => value?.isEmpty ?? true ? 'Obavezno polje' : null,
            ),
            TextFormField(
              decoration: InputDecoration(labelText: 'Do (MM/YYYY)'),
              onChanged: (value) => _do = value,
              validator: (value) => value?.isEmpty ?? true ? 'Obavezno polje' : null,
            ),
            TextFormField(
              decoration: InputDecoration(labelText: 'Pozicija'),
              onChanged: (value) => _pozicija = value,
              validator: (value) => value?.isEmpty ?? true ? 'Obavezno polje' : null,
            ),
            TextFormField(
              decoration: InputDecoration(labelText: 'Mjesto'),
              onChanged: (value) => _mjesto = value,
              validator: (value) => value?.isEmpty ?? true ? 'Obavezno polje' : null,
            ),
            DropdownButtonFormField<String>(
              decoration: InputDecoration(labelText: 'Država'),
              value: _drzava,
              items: _drzave.map((String drzava) {
                return DropdownMenuItem<String>(
                  value: drzava,
                  child: Text(drzava),
                );
              }).toList(),
              onChanged: (value) => setState(() => _drzava = value!),
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: Text('Odustani'),
        ),
        ElevatedButton(
          onPressed: () {
            if (_formKey.currentState!.validate()) {
              widget.onSave({
                'od': _od,
                'do': _do,
                'pozicija': _pozicija,
                'mjesto': _mjesto,
                'drzava': _drzava,
              });
              Navigator.pop(context);
            }
          },
          child: Text('Sačuvaj'),
        ),
      ],
    );
  }
}