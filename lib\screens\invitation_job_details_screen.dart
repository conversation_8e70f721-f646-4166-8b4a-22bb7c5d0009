import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../utils/navigation_service.dart';
import '../constants/design_constants.dart';

class InvitationJobDetailsScreen extends StatefulWidget {
  @override
  _InvitationJobDetailsScreenState createState() => _InvitationJobDetailsScreenState();
}

class _InvitationJobDetailsScreenState extends State<InvitationJobDetailsScreen> {
  final Map<String, dynamic> job = {
    'id': '1',
    'title': 'Senior Flutter Developer',
    'company': 'TechCorp GmbH',
    'country': 'Njemačka',
    'city': 'Berlin',
    'salary': '€4,500 - €6,000',
    'type': 'Puno radno vrijeme',
    'logo': '🏢',
    'countryFlag': '🇩🇪',
    'description': 'Kompanija TechCorp vas poziva da se prijavite za poziciju Senior Flutter Developer. Ovo je odličná prilika da se pridružite našem dinamičnom timu i radite na inovativnim mobilnim aplikacijama koje koriste milioni korisnika širom sveta.',
    'requirements': [
      'Njemački jezik - nivo B2 ili viši',
      'Vozačka dozvola kategorije B',
      'Minimum 3 godine iskustva sa Flutter-om',
      'Poznavanje Dart programskog jezika',
      'Iskustvo sa REST API integracijama',
      'Razumijevanje Git workflow-a',
      'Odličné komunikacijske vještine',
      'Iskustvo sa testiranjem aplikacija'
    ],
    'benefits': [
      'Fleksibilno radno vrijeme',
      'Mogućnost rada od kuće 3 dana nedeljno',
      'Privatno zdravstveno osiguranje',
      'Godišnji bonus do 15%',
      'Besplatna kafa i obroci',
      'Tim building eventi',
      'Mogućnost napredovanja i učenja',
      'Moderni radni prostor u centru Berlina',
      'Relocation paket za kandidate iz inostranstva'
    ]
  };

  @override
  Widget build(BuildContext context) {
    return Material(
      color: const Color(0xFFFAFBFC),
      child: Stack(
        children: [
          Column(
            children: [
              Expanded(
                child: SingleChildScrollView(
                  padding: EdgeInsets.only(bottom: 100),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildJobHeaderCard(),
                      _buildJobDescription(),
                      _buildRequirementsSection(),
                      _buildBenefitsSection(),
                      SizedBox(height: 80), // Space for floating buttons
                    ],
                  ),
                ),
              ),
            ],
          ),
          // Floating Action Buttons
          Positioned(
            left: DesignConstants.spaceL,
            right: DesignConstants.spaceL,
            bottom: DesignConstants.spaceL,
            child: SafeArea(
              child: _buildInvitationActionButtons(),
            ),
          ),
        ],
      ),
    );
  }


  Widget _buildJobHeaderCard() {
    return Container(
      margin: EdgeInsets.fromLTRB(DesignConstants.spaceL, DesignConstants.spaceM, DesignConstants.spaceL, 0),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            DesignConstants.white,
            DesignConstants.modernPrimaryBlue.withOpacity(0.02),
          ],
        ),
        borderRadius: BorderRadius.circular(DesignConstants.radiusXLarge),
        border: Border.all(
          color: DesignConstants.modernPrimaryBlue.withOpacity(0.1),
          width: 1.5,
        ),
        boxShadow: [
          BoxShadow(
            color: DesignConstants.modernPrimaryBlue.withOpacity(0.05),
            blurRadius: 12,
            offset: Offset(0, 4),
            spreadRadius: 1,
          ),
          BoxShadow(
            color: Colors.black.withOpacity(0.03),
            blurRadius: 8,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: EdgeInsets.all(DesignConstants.spaceXL),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        job['title'],
                        style: DesignConstants.titleLarge.copyWith(
                          color: DesignConstants.black,
                          height: 1.2,
                        ),
                      ),
                      SizedBox(height: DesignConstants.spaceXS),
                      Text(
                        job['company'],
                        style: DesignConstants.titleSmall.copyWith(
                          color: DesignConstants.modernPrimaryBlue,
                        ),
                      ),
                      SizedBox(height: DesignConstants.spaceS),
                      Row(
                        children: [
                          Text(
                            job['countryFlag'],
                            style: TextStyle(fontSize: 18),
                          ),
                          SizedBox(width: DesignConstants.spaceXS),
                          Text(
                            '${job['city']}, ${job['country']}',
                            style: DesignConstants.bodyMedium.copyWith(
                              color: DesignConstants.darkGray,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                SizedBox(width: 12),
                Text(
                  job['logo'],
                  style: TextStyle(fontSize: 48),
                ),
              ],
            ),
            SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  job['salary'],
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: const Color(0xFF1E3A8A),
                  ),
                ),
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: const Color(0xFFF1F5F9),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    job['type'],
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      color: const Color(0xFF475569),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildJobDescription() {
    return Container(
      margin: EdgeInsets.fromLTRB(DesignConstants.spaceL, DesignConstants.spaceXL, DesignConstants.spaceL, 0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Opis posla',
            style: DesignConstants.titleMedium.copyWith(
              color: DesignConstants.black,
            ),
          ),
          SizedBox(height: DesignConstants.spaceM),
          Container(
            width: double.infinity,
            decoration: DesignConstants.modernGlassCard,
            padding: EdgeInsets.all(DesignConstants.spaceXL),
            child: Text(
              job['description'],
              style: DesignConstants.bodyMedium.copyWith(
                height: 1.6,
                color: DesignConstants.darkGray,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRequirementsSection() {
    return Container(
      margin: EdgeInsets.fromLTRB(DesignConstants.spaceL, DesignConstants.spaceXL, DesignConstants.spaceL, 0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Zahtjevi',
            style: DesignConstants.titleMedium.copyWith(
              color: DesignConstants.black,
            ),
          ),
          SizedBox(height: DesignConstants.spaceM),
          Container(
            width: double.infinity,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(DesignConstants.radiusLarge),
              border: Border.all(
                color: const Color(0xFFE2E8F0),
                width: 1,
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.05),
                  blurRadius: 4,
                  offset: Offset(0, 1),
                ),
              ],
            ),
            padding: EdgeInsets.all(DesignConstants.spaceXL),
            child: Column(
              children: job['requirements'].asMap().entries.map<Widget>((entry) {
                int index = entry.key;
                String requirement = entry.value;
                IconData icon = _getRequirementIcon(index);
                
                return Padding(
                  padding: EdgeInsets.only(bottom: index < job['requirements'].length - 1 ? DesignConstants.spaceM : 0),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Container(
                        margin: EdgeInsets.only(top: 2),
                        child: Icon(
                          icon,
                          size: DesignConstants.iconSmall,
                          color: DesignConstants.modernPrimaryBlue,
                        ),
                      ),
                      SizedBox(width: DesignConstants.spaceM),
                      Expanded(
                        child: Text(
                          requirement,
                          style: DesignConstants.bodySmall.copyWith(
                            color: DesignConstants.black,
                          ),
                        ),
                      ),
                    ],
                  ),
                );
              }).toList(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBenefitsSection() {
    return Container(
      margin: EdgeInsets.fromLTRB(DesignConstants.spaceL, DesignConstants.spaceXL, DesignConstants.spaceL, 0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Benefiti',
            style: DesignConstants.titleMedium.copyWith(
              color: DesignConstants.black,
            ),
          ),
          SizedBox(height: DesignConstants.spaceM),
          Container(
            width: double.infinity,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(DesignConstants.radiusLarge),
              border: Border.all(
                color: const Color(0xFFE2E8F0),
                width: 1,
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.05),
                  blurRadius: 4,
                  offset: Offset(0, 1),
                ),
              ],
            ),
            padding: EdgeInsets.all(DesignConstants.spaceXL),
            child: Column(
              children: job['benefits'].asMap().entries.map<Widget>((entry) {
                int index = entry.key;
                String benefit = entry.value;
                IconData icon = _getBenefitIcon(index);
                
                return Padding(
                  padding: EdgeInsets.only(bottom: index < job['benefits'].length - 1 ? DesignConstants.spaceM : 0),
                  child: Row(
                    children: [
                      Container(
                        margin: EdgeInsets.only(top: 2),
                        child: Icon(
                          icon,
                          size: DesignConstants.iconSmall,
                          color: const Color(0xFF059669),
                        ),
                      ),
                      SizedBox(width: DesignConstants.spaceM),
                      Expanded(
                        child: Text(
                          benefit,
                          style: DesignConstants.bodySmall.copyWith(
                            color: DesignConstants.black,
                          ),
                        ),
                      ),
                    ],
                  ),
                );
              }).toList(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInvitationActionButtons() {
    return Row(
      children: [
        // Decline Button
        Expanded(
          child: Container(
            height: 56,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(DesignConstants.radiusLarge),
              boxShadow: [
                BoxShadow(
                  color: const Color(0xFFEF4444).withOpacity(0.2),
                  blurRadius: 12,
                  offset: Offset(0, 6),
                ),
              ],
            ),
            child: OutlinedButton(
              onPressed: () {
                _showDeclineDialog();
              },
              style: OutlinedButton.styleFrom(
                backgroundColor: DesignConstants.white,
                side: BorderSide(color: const Color(0xFFEF4444), width: 1.5),
                elevation: 0,
                shadowColor: Colors.transparent,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(DesignConstants.radiusLarge),
                ),
                padding: EdgeInsets.symmetric(vertical: DesignConstants.spaceM),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.close_rounded,
                    size: DesignConstants.iconMedium,
                    color: const Color(0xFFEF4444),
                  ),
                  SizedBox(width: DesignConstants.spaceS),
                  Text(
                    'Odbij',
                    style: DesignConstants.buttonLarge.copyWith(
                      color: const Color(0xFFEF4444),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
        SizedBox(width: DesignConstants.spaceM),
        // Accept Button
        Expanded(
          child: Container(
            height: 56,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(DesignConstants.radiusLarge),
              boxShadow: DesignConstants.modernBrandButtonShadow,
            ),
            child: ElevatedButton(
              onPressed: () {
                _showAcceptDialog();
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: DesignConstants.modernPrimaryBlue,
                foregroundColor: DesignConstants.white,
                elevation: 0,
                shadowColor: Colors.transparent,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(DesignConstants.radiusLarge),
                ),
                padding: EdgeInsets.symmetric(vertical: DesignConstants.spaceM),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.check_rounded,
                    size: DesignConstants.iconMedium,
                  ),
                  SizedBox(width: DesignConstants.spaceS),
                  Text(
                    'Prijavi se',
                    style: DesignConstants.buttonLarge,
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  IconData _getRequirementIcon(int index) {
    switch (index) {
      case 0:
        return Icons.language;
      case 1:
        return Icons.directions_car;
      case 2:
        return Icons.code;
      case 3:
        return Icons.integration_instructions;
      case 4:
        return Icons.api;
      case 5:
        return Icons.source;
      default:
        return Icons.check_circle_outline;
    }
  }

  IconData _getBenefitIcon(int index) {
    switch (index) {
      case 0:
        return Icons.access_time;
      case 1:
        return Icons.home_work;
      case 2:
        return Icons.security;
      case 3:
        return Icons.card_giftcard;
      case 4:
        return Icons.restaurant;
      case 5:
        return Icons.people;
      case 6:
        return Icons.trending_up;
      case 7:
        return Icons.business;
      case 8:
        return Icons.flight_takeoff;
      default:
        return Icons.check_circle_outline;
    }
  }

  void _showAcceptDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        title: Row(
          children: [
            Container(
              padding: EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: const Color(0xFF10B981).withOpacity(0.1),
                shape: BoxShape.circle,
              ),
              child: Icon(
                Icons.check_rounded,
                color: const Color(0xFF10B981),
                size: 24,
              ),
            ),
            SizedBox(width: 12),
            Text(
              'Prijavi se',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: const Color(0xFF0F172A),
              ),
            ),
          ],
        ),
        content: Text(
          'Da li ste sigurni da se želite prijaviti za poziciju "${job['title']}" u kompaniji ${job['company']}?',
          style: TextStyle(
            fontSize: 16,
            color: const Color(0xFF64748B),
            height: 1.5,
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'Otkaži',
              style: TextStyle(
                color: const Color(0xFF64748B),
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              NavigationService().navigateBack(); // Go back to invitations
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Row(
                    children: [
                      Icon(Icons.check_circle, color: Colors.white),
                      SizedBox(width: 12),
                      Text('Uspešno ste se prijavili na posao!'),
                    ],
                  ),
                  backgroundColor: const Color(0xFF10B981),
                  behavior: SnackBarBehavior.floating,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF10B981),
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: Text(
              'Prijavi se',
              style: TextStyle(fontWeight: FontWeight.w600),
            ),
          ),
        ],
      ),
    );
  }

  void _showDeclineDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        title: Row(
          children: [
            Container(
              padding: EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: const Color(0xFFEF4444).withOpacity(0.1),
                shape: BoxShape.circle,
              ),
              child: Icon(
                Icons.close_rounded,
                color: const Color(0xFFEF4444),
                size: 24,
              ),
            ),
            SizedBox(width: 12),
            Text(
              'Odbij',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: const Color(0xFF0F172A),
              ),
            ),
          ],
        ),
        content: Text(
          'Da li ste sigurni da želite da odbijete pozivnicu za poziciju "${job['title']}" u kompaniji ${job['company']}?\n\nOva akcija se ne može poništiti.',
          style: TextStyle(
            fontSize: 16,
            color: const Color(0xFF64748B),
            height: 1.5,
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'Otkaži',
              style: TextStyle(
                color: const Color(0xFF64748B),
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              NavigationService().navigateBack(); // Go back to invitations
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Row(
                    children: [
                      Icon(Icons.info_outline, color: Colors.white),
                      SizedBox(width: 12),
                      Text('Pozivnica je odbijena.'),
                    ],
                  ),
                  backgroundColor: const Color(0xFF64748B),
                  behavior: SnackBarBehavior.floating,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFFEF4444),
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: Text(
              'Odbij',
              style: TextStyle(fontWeight: FontWeight.w600),
            ),
          ),
        ],
      ),
    );
  }
}