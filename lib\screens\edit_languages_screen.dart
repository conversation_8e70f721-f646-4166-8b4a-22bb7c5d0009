import 'package:flutter/material.dart';
import '../constants/design_constants.dart';

class EditLanguagesScreen extends StatefulWidget {
  @override
  _EditLanguagesScreenState createState() => _EditLanguagesScreenState();
}

class _EditLanguagesScreenState extends State<EditLanguagesScreen> {
  bool _hasChanges = false;
  
  // Language levels according to CEFR
  final Map<String, String> _languageLevels = {
    'a1': 'A1 - Početno',
    'a2': 'A2 - Osnovno',
    'b1': 'B1 - Srednje',
    'b2': 'B2 - Više srednje',
    'c1': 'C1 - Nap<PERSON>no',
    'c2': 'C2 - <PERSON><PERSON><PERSON><PERSON>',
  };
  
  // Current language levels - initialize with existing data
  String _germanLevel = 'b2';
  String _englishLevel = 'b1';
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.center,
            colors: [
              DesignConstants.modernPrimaryBlue,
              DesignConstants.modernPrimaryBlue.withOpacity(0.1),
            ],
            stops: [0.0, 0.3],
          ),
        ),
        child: SafeArea(
          child: Stack(
            children: [
              Column(
                children: [
                  // Custom AppBar with gradient
                  Container(
                    padding: EdgeInsets.symmetric(horizontal: 20, vertical: 16),
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.15),
                      border: Border(
                        bottom: BorderSide(
                          color: Colors.white.withOpacity(0.2),
                          width: 1,
                        ),
                      ),
                    ),
                    child: Row(
                      children: [
                        Container(
                          decoration: BoxDecoration(
                            color: Colors.white.withOpacity(0.2),
                            borderRadius: BorderRadius.circular(DesignConstants.radiusMedium),
                            border: Border.all(
                              color: Colors.white.withOpacity(0.3),
                              width: 1,
                            ),
                          ),
                          child: IconButton(
                            onPressed: () => Navigator.pop(context),
                            icon: Icon(
                              Icons.arrow_back_ios,
                              color: DesignConstants.white,
                              size: DesignConstants.iconMedium,
                            ),
                            padding: EdgeInsets.all(8),
                          ),
                        ),
                        Expanded(
                          child: Text(
                            'Uredi jezičko znanje',
                            textAlign: TextAlign.center,
                            style: DesignConstants.titleMedium.copyWith(
                              color: DesignConstants.white,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                        // Invisible container for balance
                        Container(width: 48, height: 48),
                      ],
                    ),
                  ),
                  // Content area
                  Expanded(
                    child: Container(
                      width: double.infinity,
                      decoration: BoxDecoration(
                        color: DesignConstants.surfaceGray,
                        borderRadius: BorderRadius.only(
                          topLeft: Radius.circular(DesignConstants.radiusXLarge),
                          topRight: Radius.circular(DesignConstants.radiusXLarge),
                        ),
                      ),
                      child: SingleChildScrollView(
                        padding: EdgeInsets.fromLTRB(20, 20, 20, 100), // Bottom padding for floating button
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            _buildModernHeader(),
                            SizedBox(height: DesignConstants.spaceXXL),
                            _buildModernLanguageSelection(),
                          ],
                        ),
                      ),
                    ),
                  ),
                ],
              ),
              
              // Floating Save Button
              if (_hasChanges)
                Positioned(
                  left: 20,
                  right: 20,
                  bottom: 20,
                  child: _buildFloatingSaveButton(),
                ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildModernHeader() {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(DesignConstants.spaceXXL),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            DesignConstants.white,
            DesignConstants.modernPrimaryBlue.withOpacity(0.02),
          ],
        ),
        borderRadius: BorderRadius.circular(DesignConstants.radiusXXLarge),
        border: Border.all(
          color: DesignConstants.modernPrimaryBlue.withOpacity(0.15),
          width: 1.5,
        ),
        boxShadow: [
          BoxShadow(
            color: DesignConstants.modernPrimaryBlue.withOpacity(0.08),
            blurRadius: 20,
            offset: Offset(0, 8),
            spreadRadius: 1,
          ),
          BoxShadow(
            color: Colors.black.withOpacity(0.03),
            blurRadius: 8,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: EdgeInsets.all(DesignConstants.spaceM),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      DesignConstants.modernPrimaryBlue.withOpacity(0.1),
                      DesignConstants.modernPrimaryBlue.withOpacity(0.05),
                    ],
                  ),
                  borderRadius: BorderRadius.circular(DesignConstants.radiusLarge),
                ),
                child: Icon(
                  Icons.language_outlined,
                  color: DesignConstants.modernPrimaryBlue,
                  size: DesignConstants.iconLarge,
                ),
              ),
              SizedBox(width: DesignConstants.spaceL),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Jezičko znanje',
                      style: DesignConstants.titleMedium.copyWith(
                        color: DesignConstants.black,
                      ),
                    ),
                    SizedBox(height: DesignConstants.spaceXS),
                    Text(
                      'Uredite svoje znanje njemačkog i engleskog jezika',
                      style: DesignConstants.bodyMedium.copyWith(
                        color: DesignConstants.darkGray,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildModernLanguageSelection() {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(DesignConstants.spaceXXL),
      decoration: BoxDecoration(
        color: DesignConstants.white,
        borderRadius: BorderRadius.circular(DesignConstants.radiusXLarge),
        border: Border.all(
          color: DesignConstants.lightGray.withOpacity(0.5),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.04),
            blurRadius: 16,
            offset: Offset(0, 4),
          ),
          BoxShadow(
            color: Colors.black.withOpacity(0.02),
            blurRadius: 4,
            offset: Offset(0, 1),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: EdgeInsets.all(6),
                decoration: BoxDecoration(
                  color: DesignConstants.modernPrimaryBlue.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(6),
                ),
                child: Icon(
                  Icons.translate_outlined,
                  size: 16,
                  color: DesignConstants.modernPrimaryBlue,
                ),
              ),
              SizedBox(width: DesignConstants.spaceS),
              Text(
                'Nivo poznavanja jezika',
                style: DesignConstants.labelMedium.copyWith(
                  color: DesignConstants.black,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          SizedBox(height: DesignConstants.spaceL),
          Text(
            'Odaberite nivo poznavanja prema CEFR standardu:',
            style: DesignConstants.bodyMedium.copyWith(
              color: DesignConstants.darkGray,
            ),
          ),
          SizedBox(height: DesignConstants.spaceXL),
          
          // German language
          _buildModernLanguageDropdown(
            'Njemački jezik',
            _germanLevel,
            Icons.flag_outlined,
            (String? newValue) {
              if (newValue != null && newValue != _germanLevel) {
                setState(() {
                  _germanLevel = newValue;
                  _hasChanges = true;
                });
              }
            },
          ),
          
          SizedBox(height: DesignConstants.spaceXXL),
          
          // English language
          _buildModernLanguageDropdown(
            'Engleski jezik',
            _englishLevel,
            Icons.flag_outlined,
            (String? newValue) {
              if (newValue != null && newValue != _englishLevel) {
                setState(() {
                  _englishLevel = newValue;
                  _hasChanges = true;
                });
              }
            },
          ),
          
          SizedBox(height: DesignConstants.spaceXL),
          
          // CEFR explanation
          _buildCEFRInfo(),
        ],
      ),
    );
  }

  Widget _buildModernLanguageDropdown(
    String language,
    String currentLevel,
    IconData icon,
    ValueChanged<String?> onChanged,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Container(
              padding: EdgeInsets.all(4),
              decoration: BoxDecoration(
                color: DesignConstants.darkGray.withOpacity(0.1),
                borderRadius: BorderRadius.circular(4),
              ),
              child: Icon(
                icon,
                size: 16,
                color: DesignConstants.darkGray,
              ),
            ),
            SizedBox(width: DesignConstants.spaceS),
            Text(
              language,
              style: DesignConstants.labelMedium.copyWith(
                color: DesignConstants.black,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
        SizedBox(height: DesignConstants.spaceM),
        Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(DesignConstants.radiusMedium),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.02),
                blurRadius: 4,
                offset: Offset(0, 2),
              ),
            ],
          ),
          child: DropdownButtonFormField<String>(
            value: currentLevel,
            onChanged: onChanged,
            decoration: InputDecoration(
              filled: true,
              fillColor: DesignConstants.surfaceGray,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(DesignConstants.radiusMedium),
                borderSide: BorderSide(
                  color: DesignConstants.lightGray.withOpacity(0.3),
                  width: 1.5,
                ),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(DesignConstants.radiusMedium),
                borderSide: BorderSide(
                  color: DesignConstants.lightGray.withOpacity(0.3),
                  width: 1.5,
                ),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(DesignConstants.radiusMedium),
                borderSide: BorderSide(
                  color: DesignConstants.modernPrimaryBlue,
                  width: 2,
                ),
              ),
              contentPadding: EdgeInsets.symmetric(
                horizontal: DesignConstants.spaceL,
                vertical: DesignConstants.spaceM + 2,
              ),
            ),
            items: _languageLevels.entries.map((entry) {
              return DropdownMenuItem<String>(
                value: entry.key,
                child: Text(
                  entry.value,
                  style: DesignConstants.bodyLarge.copyWith(
                    color: DesignConstants.black,
                  ),
                ),
              );
            }).toList(),
          ),
        ),
      ],
    );
  }

  Widget _buildCEFRInfo() {
    return Container(
      padding: EdgeInsets.all(DesignConstants.spaceL),
      decoration: BoxDecoration(
        color: DesignConstants.modernPrimaryBlue.withOpacity(0.05),
        borderRadius: BorderRadius.circular(DesignConstants.radiusMedium),
        border: Border.all(
          color: DesignConstants.modernPrimaryBlue.withOpacity(0.1),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.info_outline,
                size: 16,
                color: DesignConstants.modernPrimaryBlue,
              ),
              SizedBox(width: DesignConstants.spaceS),
              Text(
                'CEFR nivoi objašnjeni',
                style: DesignConstants.labelMedium.copyWith(
                  color: DesignConstants.modernPrimaryBlue,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          SizedBox(height: DesignConstants.spaceM),
          ...{
            'A1 - Početno': 'Razumijem osnove, mogu se predstaviti',
            'A2 - Osnovno': 'Mogu komunicirati u jednostavnim situacijama',
            'B1 - Srednje': 'Mogu se služiti jezikom u poznatim situacijama',
            'B2 - Više srednje': 'Mogu se izraziti jasno o različitim temama',
            'C1 - Napredno': 'Tečno se izražavam u složenim situacijama',
            'C2 - Majstorsko': 'Razumijem praktički sve i izražavam se precizno',
          }.entries.map((entry) => Padding(
            padding: EdgeInsets.only(bottom: DesignConstants.spaceXS),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  width: 4,
                  height: 4,
                  margin: EdgeInsets.only(top: 6),
                  decoration: BoxDecoration(
                    color: DesignConstants.modernPrimaryBlue,
                    shape: BoxShape.circle,
                  ),
                ),
                SizedBox(width: DesignConstants.spaceS),
                Expanded(
                  child: RichText(
                    text: TextSpan(
                      children: [
                        TextSpan(
                          text: entry.key.split(' - ')[0] + ' - ',
                          style: DesignConstants.bodySmall.copyWith(
                            color: DesignConstants.modernPrimaryBlue,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        TextSpan(
                          text: entry.value,
                          style: DesignConstants.bodySmall.copyWith(
                            color: DesignConstants.darkGray,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          )),
        ],
      ),
    );
  }

  Widget _buildFloatingSaveButton() {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        gradient: DesignConstants.modernPrimaryGradient,
        borderRadius: BorderRadius.circular(DesignConstants.radiusLarge),
        boxShadow: [
          BoxShadow(
            color: DesignConstants.modernPrimaryBlue.withOpacity(0.4),
            blurRadius: 20,
            offset: Offset(0, 8),
          ),
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: _saveChanges,
          borderRadius: BorderRadius.circular(DesignConstants.radiusLarge),
          child: Container(
            padding: EdgeInsets.symmetric(
              vertical: DesignConstants.spaceL + 2,
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.check_circle_outline,
                  color: DesignConstants.white,
                  size: DesignConstants.iconMedium,
                ),
                SizedBox(width: DesignConstants.spaceS),
                Text(
                  'Spremi promjene',
                  style: DesignConstants.buttonLarge.copyWith(
                    color: DesignConstants.white,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _saveChanges() {
    // TODO: Save changes to backend/state management
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Container(
          padding: EdgeInsets.symmetric(vertical: 4),
          child: Row(
            children: [
              Container(
                padding: EdgeInsets.all(6),
                decoration: BoxDecoration(
                  color: DesignConstants.white.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Icon(
                  Icons.check_circle,
                  color: DesignConstants.white,
                  size: 16,
                ),
              ),
              SizedBox(width: DesignConstants.spaceM),
              Expanded(
                child: Text(
                  'Jezičko znanje uspješno ažurirano!',
                  style: DesignConstants.labelMedium.copyWith(
                    color: DesignConstants.white,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
        ),
        backgroundColor: DesignConstants.modernSuccessGreen,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(DesignConstants.radiusMedium),
        ),
        margin: EdgeInsets.all(DesignConstants.spaceL),
        elevation: 8,
        duration: Duration(seconds: 3),
      ),
    );
    
    Navigator.pop(context, true);
  }
}
