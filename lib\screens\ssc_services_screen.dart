import 'package:flutter/material.dart';
import '../constants/design_constants.dart';

class SSCServicesScreen extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Material(
      color: DesignConstants.backgroundLight,
      child: Stack(
        children: [
          Column(
            children: [
              Expanded(
                child: SingleChildScrollView(
                  padding: EdgeInsets.only(bottom: 100),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildCompanyIntro(),
                      _buildServicesSection(),
                      _buildPricingSection(),
                      _buildWhyChooseUsSection(),
                    ],
                  ),
                ),
              ),
            ],
          ),
          // Floating Contact Button
          Positioned(
            bottom: DesignConstants.spaceL,
            left: DesignConstants.spaceL,
            right: DesignConstants.spaceL,
            child: _buildFloatingContactButton(context),
          ),
        ],
      ),
    );
  }


  Widget _buildCompanyIntro() {
    return Container(
      margin: EdgeInsets.fromLTRB(DesignConstants.spaceXL, DesignConstants.spaceL, DesignConstants.spaceXL, 0),
      padding: EdgeInsets.all(DesignConstants.spaceXL),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            DesignConstants.successGreen.withOpacity(0.05),
            DesignConstants.successGreen.withOpacity(0.1),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(DesignConstants.radiusLarge),
        border: Border.all(
          color: DesignConstants.successGreen.withOpacity(0.2),
        ),
        boxShadow: DesignConstants.shadowMedium,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: EdgeInsets.all(DesignConstants.spaceM),
                decoration: BoxDecoration(
                  color: DesignConstants.successGreen,
                  borderRadius: BorderRadius.circular(DesignConstants.radiusMedium),
                ),
                child: Text(
                  'SSC',
                  style: TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    fontSize: 18,
                    letterSpacing: 1.2,
                  ),
                ),
              ),
              SizedBox(width: DesignConstants.spaceL),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'SSC d.o.o.',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: DesignConstants.textPrimary,
                      ),
                    ),
                    Text(
                      'Stručni partner za vize i dokumentaciju',
                      style: TextStyle(
                        fontSize: 14,
                        color: DesignConstants.textSecondary,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          SizedBox(height: DesignConstants.spaceL),
          Text(
            'Olakšavamo vam proces dobijanja radnih dozvola, viza i sređivanja sve potrebne dokumentacije za rad u inostranstvu.',
            style: TextStyle(
              fontSize: 16,
              color: DesignConstants.textPrimary,
              height: 1.5,
            ),
          ),
          SizedBox(height: DesignConstants.spaceM),
          Row(
            children: [
              Icon(
                Icons.verified,
                color: DesignConstants.successGreen,
                size: DesignConstants.iconSmall,
              ),
              SizedBox(width: DesignConstants.spaceS),
              Text(
                'Licencirani i sertifikovani stručnjaci',
                style: TextStyle(
                  fontSize: 14,
                  color: DesignConstants.successGreen,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildServicesSection() {
    final services = [
      {
        'icon': Icons.description_outlined,
        'title': 'Ispunjavanje obrazaca',
        'description': 'Profesionalno ispunjavanje VIDEX obrazaca i drugih zahteva',
        'color': DesignConstants.primaryBlue,
      },
      {
        'icon': Icons.folder_special_outlined,
        'title': 'Slaganje dokumentacije',
        'description': 'Priprema i organizacija svih potrebnih dokumenata',
        'color': DesignConstants.accentOrange,
      },
      {
        'icon': Icons.calendar_today_outlined,
        'title': 'Zakazivanje termina',
        'description': 'Zakazivanje termina u ambasadama i konzulatima',
        'color': DesignConstants.secondaryBlue,
      },
      {
        'icon': Icons.support_agent_outlined,
        'title': 'Konsultacije',
        'description': 'Stručni saveti i pomoć tokom celog procesa',
        'color': Color(0xFF7C3AED),
      },
    ];

    return Container(
      margin: EdgeInsets.fromLTRB(DesignConstants.spaceXL, DesignConstants.spaceXXL, DesignConstants.spaceXL, 0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Naše usluge',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: DesignConstants.textPrimary,
            ),
          ),
          SizedBox(height: DesignConstants.spaceL),
          ...services.map((service) => _buildServiceCard(service)).toList(),
        ],
      ),
    );
  }

  Widget _buildServiceCard(Map<String, dynamic> service) {
    return Container(
      margin: EdgeInsets.only(bottom: DesignConstants.spaceL),
      padding: EdgeInsets.all(DesignConstants.spaceL),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(DesignConstants.radiusMedium),
        border: Border.all(color: DesignConstants.borderGray),
        boxShadow: DesignConstants.shadowSmall,
      ),
      child: Row(
        children: [
          Container(
            padding: EdgeInsets.all(DesignConstants.spaceM),
            decoration: BoxDecoration(
              color: service['color'].withOpacity(0.1),
              borderRadius: BorderRadius.circular(DesignConstants.radiusMedium),
            ),
            child: Icon(
              service['icon'],
              color: service['color'],
              size: DesignConstants.iconLarge,
            ),
          ),
          SizedBox(width: DesignConstants.spaceL),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  service['title'],
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: DesignConstants.textPrimary,
                  ),
                ),
                SizedBox(height: DesignConstants.spaceXS),
                Text(
                  service['description'],
                  style: TextStyle(
                    fontSize: 14,
                    color: DesignConstants.textSecondary,
                    height: 1.4,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPricingSection() {
    final pricing = [
      {
        'service': 'Ispunjavanje VIDEX obrasca',
        'price': '200,00 KM',
        'icon': Icons.description_rounded,
        'popular': true,
      },
      {
        'service': 'Slaganje dokumentacije',
        'price': '50,00 KM',
        'icon': Icons.folder_open_rounded,
        'popular': false,
      },
      {
        'service': 'Zakazivanje termina',
        'price': '100,00 KM',
        'icon': Icons.event_rounded,
        'popular': false,
      },
      {
        'service': 'Kompletna usluga',
        'price': '320,00 KM',
        'icon': Icons.workspace_premium_rounded,
        'popular': true,
        'originalPrice': '350,00 KM',
      },
    ];

    return Container(
      margin: EdgeInsets.fromLTRB(DesignConstants.spaceXL, DesignConstants.spaceXXL, DesignConstants.spaceXL, 0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text(
                'Cenovnik usluga',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: DesignConstants.textPrimary,
                ),
              ),
              SizedBox(width: DesignConstants.spaceS),
              Container(
                padding: EdgeInsets.symmetric(
                  horizontal: DesignConstants.spaceS,
                  vertical: DesignConstants.spaceXS,
                ),
                decoration: BoxDecoration(
                  color: DesignConstants.accentOrange.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(DesignConstants.radiusSmall),
                ),
                child: Text(
                  'Transparentno',
                  style: TextStyle(
                    fontSize: 11,
                    fontWeight: FontWeight.w600,
                    color: DesignConstants.accentOrange,
                  ),
                ),
              ),
            ],
          ),
          SizedBox(height: DesignConstants.spaceL),
          ...pricing.map((item) => _buildPricingCard(item)).toList(),
        ],
      ),
    );
  }

  Widget _buildPricingCard(Map<String, dynamic> item) {
    return Container(
      margin: EdgeInsets.only(bottom: DesignConstants.spaceM),
      padding: EdgeInsets.all(DesignConstants.spaceL),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(DesignConstants.radiusMedium),
        border: Border.all(
          color: item['popular'] == true 
              ? DesignConstants.successGreen 
              : DesignConstants.borderGray,
          width: item['popular'] == true ? 2 : 1,
        ),
        boxShadow: item['popular'] == true 
            ? DesignConstants.shadowColored(DesignConstants.successGreen)
            : DesignConstants.shadowSmall,
      ),
      child: Row(
        children: [
          Container(
            padding: EdgeInsets.all(DesignConstants.spaceS),
            decoration: BoxDecoration(
              color: item['popular'] == true 
                  ? DesignConstants.successGreen.withOpacity(0.1)
                  : DesignConstants.borderGray.withOpacity(0.3),
              borderRadius: BorderRadius.circular(DesignConstants.radiusSmall),
            ),
            child: Icon(
              item['icon'],
              color: item['popular'] == true 
                  ? DesignConstants.successGreen 
                  : DesignConstants.textSecondary,
              size: DesignConstants.iconMedium,
            ),
          ),
          SizedBox(width: DesignConstants.spaceL),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      item['service'],
                      style: TextStyle(
                        fontSize: 15,
                        fontWeight: FontWeight.w600,
                        color: DesignConstants.textPrimary,
                      ),
                    ),
                    if (item['popular'] == true) ...[
                      SizedBox(height: DesignConstants.spaceXS),
                      Container(
                        padding: EdgeInsets.symmetric(
                          horizontal: DesignConstants.spaceS,
                          vertical: 2,
                        ),
                        decoration: BoxDecoration(
                          color: DesignConstants.successGreen,
                          borderRadius: BorderRadius.circular(DesignConstants.radiusSmall),
                        ),
                        child: Text(
                          'POPULARNO',
                          style: TextStyle(
                            fontSize: 10,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                            letterSpacing: 0.5,
                          ),
                        ),
                      ),
                    ],
                  ],
                ),
                if (item['originalPrice'] != null) ...[
                  SizedBox(height: DesignConstants.spaceS),
                  Row(
                    children: [
                      Flexible(
                        child: Text(
                          item['originalPrice'],
                          style: TextStyle(
                            fontSize: 13,
                            color: DesignConstants.textSecondary,
                            decoration: TextDecoration.lineThrough,
                          ),
                        ),
                      ),
                      SizedBox(width: DesignConstants.spaceS),
                      Container(
                        padding: EdgeInsets.symmetric(
                          horizontal: DesignConstants.spaceS,
                          vertical: 2,
                        ),
                        decoration: BoxDecoration(
                          color: DesignConstants.errorRed.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(DesignConstants.radiusSmall),
                        ),
                        child: Text(
                          'UŠTEDA',
                          style: TextStyle(
                            fontSize: 10,
                            fontWeight: FontWeight.w600,
                            color: DesignConstants.errorRed,
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ],
            ),
          ),
          Text(
            item['price'],
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: item['popular'] == true 
                  ? DesignConstants.successGreen 
                  : DesignConstants.textPrimary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildWhyChooseUsSection() {
    final reasons = [
      'Over 1000+ uspešno obrađenih slučajeva',
      'Prosečno vreme obrade: 5-7 radnih dana',
      'Stopa odobravanja viša od 95%',
      'Podrška na bosanskom jeziku',
      'Povraćaj novca ako zahtev nije odobren*',
    ];

    return Container(
      margin: EdgeInsets.all(DesignConstants.spaceXL),
      padding: EdgeInsets.all(DesignConstants.spaceL),
      decoration: BoxDecoration(
        color: DesignConstants.backgroundGray,
        borderRadius: BorderRadius.circular(DesignConstants.radiusMedium),
        border: Border.all(color: DesignConstants.borderGray),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Zašto odabrati nas?',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: DesignConstants.textPrimary,
            ),
          ),
          SizedBox(height: DesignConstants.spaceL),
          ...reasons.map((reason) => Container(
            margin: EdgeInsets.only(bottom: DesignConstants.spaceS),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Icon(
                  Icons.check_circle,
                  color: DesignConstants.successGreen,
                  size: DesignConstants.iconSmall,
                ),
                SizedBox(width: DesignConstants.spaceS),
                Expanded(
                  child: Text(
                    reason,
                    style: TextStyle(
                      fontSize: 14,
                      color: DesignConstants.textSecondary,
                      height: 1.4,
                    ),
                  ),
                ),
              ],
            ),
          )).toList(),
        ],
      ),
    );
  }

  Widget _buildFloatingContactButton(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        boxShadow: DesignConstants.shadowLarge,
        borderRadius: BorderRadius.circular(DesignConstants.radiusLarge),
      ),
      child: ElevatedButton(
        onPressed: () {
          _showContactDialog(context);
        },
        style: ElevatedButton.styleFrom(
          backgroundColor: DesignConstants.successGreen,
          foregroundColor: Colors.white,
          elevation: 8,
          shadowColor: DesignConstants.successGreen.withOpacity(0.4),
          padding: EdgeInsets.symmetric(
            vertical: DesignConstants.spaceL,
            horizontal: DesignConstants.spaceXL,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(DesignConstants.radiusLarge),
          ),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: EdgeInsets.all(DesignConstants.spaceXS),
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.2),
                shape: BoxShape.circle,
              ),
              child: Icon(
                Icons.contact_support,
                size: DesignConstants.iconMedium,
                color: Colors.white,
              ),
            ),
            SizedBox(width: DesignConstants.spaceM),
            Flexible(
              child: Text(
                'Kontaktiraj SSC',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  letterSpacing: 0.5,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Keep the old function for reference but it's not used anymore
  Widget _buildContactButton(BuildContext context) {
    return Container(
      color: Colors.white,
      child: SafeArea(
        child: Container(
          padding: EdgeInsets.all(DesignConstants.spaceL),
          decoration: BoxDecoration(
            color: Colors.white,
            border: Border(
              top: BorderSide(
                color: DesignConstants.borderGray,
                width: 1,
              ),
            ),
          ),
          child: SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: () {
                _showContactDialog(context);
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: DesignConstants.successGreen,
                foregroundColor: Colors.white,
                elevation: 0,
                padding: EdgeInsets.symmetric(vertical: DesignConstants.spaceL),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(DesignConstants.radiusMedium),
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.contact_support, size: DesignConstants.iconMedium),
                  SizedBox(width: DesignConstants.spaceS),
                  Text(
                    'Kontaktiraj SSC',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  void _showContactDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(DesignConstants.radiusLarge),
        ),
        title: Row(
          children: [
            Container(
              padding: EdgeInsets.all(DesignConstants.spaceS),
              decoration: BoxDecoration(
                color: DesignConstants.successGreen.withOpacity(0.1),
                shape: BoxShape.circle,
              ),
              child: Icon(
                Icons.contact_support,
                color: DesignConstants.successGreen,
                size: DesignConstants.iconLarge,
              ),
            ),
            SizedBox(width: DesignConstants.spaceM),
            Expanded(
              child: Text(
                'Kontakt SSC d.o.o.',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: DesignConstants.textPrimary,
                ),
              ),
            ),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Kontaktirajte stručnjake iz SSC d.o.o. za pomoć oko vize i dokumentacije.',
              style: TextStyle(
                fontSize: 16,
                color: DesignConstants.textSecondary,
                height: 1.5,
              ),
            ),
            SizedBox(height: DesignConstants.spaceL),
            Container(
              padding: EdgeInsets.all(DesignConstants.spaceM),
              decoration: BoxDecoration(
                color: DesignConstants.successGreen.withOpacity(0.1),
                borderRadius: BorderRadius.circular(DesignConstants.radiusSmall),
              ),
              child: Column(
                children: [
                  Row(
                    children: [
                      Icon(Icons.phone, color: DesignConstants.successGreen, size: DesignConstants.iconSmall),
                      SizedBox(width: DesignConstants.spaceS),
                      Text(
                        '+387 33 123 456',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: DesignConstants.textPrimary,
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: DesignConstants.spaceS),
                  Row(
                    children: [
                      Icon(Icons.email, color: DesignConstants.successGreen, size: DesignConstants.iconSmall),
                      SizedBox(width: DesignConstants.spaceS),
                      Text(
                        '<EMAIL>',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: DesignConstants.textPrimary,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'Zatvori',
              style: TextStyle(
                color: DesignConstants.textSecondary,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Row(
                    children: [
                      Icon(Icons.phone, color: Colors.white),
                      SizedBox(width: DesignConstants.spaceM),
                      Text('Pozivam SSC d.o.o...'),
                    ],
                  ),
                  backgroundColor: DesignConstants.successGreen,
                  behavior: SnackBarBehavior.floating,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(DesignConstants.radiusMedium),
                  ),
                ),
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: DesignConstants.successGreen,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(DesignConstants.radiusSmall),
              ),
            ),
            child: Text(
              'Pozovi sada',
              style: TextStyle(fontWeight: FontWeight.w600),
            ),
          ),
        ],
      ),
    );
  }
}