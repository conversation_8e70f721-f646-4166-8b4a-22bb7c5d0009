import 'package:flutter/material.dart';
import '../constants/design_constants.dart';

class SearchBarWithFilter extends StatefulWidget {
  final Function(String) onSearch;

  const SearchBarWithFilter({Key? key, required this.onSearch}) : super(key: key);

  @override
  _SearchBarWithFilterState createState() => _SearchBarWithFilterState();
}

class _SearchBarWithFilterState extends State<SearchBarWithFilter> {
  final TextEditingController _searchController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: DesignConstants.backgroundGray,
        borderRadius: BorderRadius.circular(DesignConstants.radiusMedium),
        border: Border.all(
          color: DesignConstants.borderGray,
          width: 1,
        ),
      ),
      child: Text<PERSON>ield(
        controller: _searchController,
        onChanged: widget.onSearch,
        decoration: InputDecoration(
          hintText: 'Pretražite poslove...',
          hintStyle: TextStyle(
            color: DesignConstants.textSecondary,
            fontSize: 16,
          ),
          prefixIcon: Icon(
            Icons.search,
            color: DesignConstants.textSecondary,
            size: DesignConstants.iconMedium,
          ),
          suffixIcon: _searchController.text.isNotEmpty
              ? GestureDetector(
                  onTap: () {
                    _searchController.clear();
                    widget.onSearch('');
                    setState(() {});
                  },
                  child: Icon(
                    Icons.clear,
                    color: DesignConstants.textSecondary,
                    size: DesignConstants.iconMedium,
                  ),
                )
              : null,
          border: InputBorder.none,
          contentPadding: EdgeInsets.symmetric(
            horizontal: DesignConstants.spaceL,
            vertical: DesignConstants.spaceL,
          ),
        ),
        style: TextStyle(
          color: DesignConstants.textPrimary,
          fontSize: 16,
        ),
      ),
    );
  }


  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }
}