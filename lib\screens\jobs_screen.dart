import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../widgets/job_card.dart';
import '../widgets/search_bar_with_filter.dart';
import '../constants/design_constants.dart';
import 'dart:async';

class JobsScreen extends StatefulWidget {
  @override
  _JobsScreenState createState() => _JobsScreenState();
}

class _JobsScreenState extends State<JobsScreen> {
  String _searchQuery = '';
  final PageController _pageController = PageController();
  Timer? _carouselTimer;
  int _currentIndex = 0;
  
  final List<Job> _featuredJobs = [
    Job(
      id: '1',
      title: 'Senior Software Engineer',
      company: 'TechCorp GmbH',
      country: 'Njemačka',
      city: 'Berlin',
      salary: '€4,500 - €6,000',
      logo: '🏢',
      countryFlag: '🇩🇪',
    ),
    Job(
      id: '2',
      title: 'Marketing Manager',
      company: 'Alpine Solutions',
      country: 'Austrija',
      city: 'Wien',
      salary: '€3,800 - €5,200',
      logo: '🏢',
      countryFlag: '🇦🇹',
    ),
    Job(
      id: '3',
      title: 'Data Analyst',
      company: 'DataFlow AG',
      country: 'Njemačka',
      city: 'München',
      salary: '€3,200 - €4,500',
      logo: '🏢',
      countryFlag: '🇩🇪',
    ),
  ];

  final List<Job> _regularJobs = [
    Job(
      id: '4',
      title: 'Frontend Developer',
      company: 'WebSolutions',
      country: 'Njemačka',
      city: 'Hamburg',
      salary: '€3,000 - €4,200',
      type: 'Puno radno vrijeme',
      logo: '🏢',
      countryFlag: '🇩🇪',
    ),
    Job(
      id: '5',
      title: 'Project Manager',
      company: 'BuildTech',
      country: 'Austrija',
      city: 'Salzburg',
      salary: '€4,000 - €5,500',
      type: 'Hibridno',
      logo: '🏢',
      countryFlag: '🇦🇹',
    ),
    Job(
      id: '6',
      title: 'UX Designer',
      company: 'DesignStudio',
      country: 'Njemačka',
      city: 'Frankfurt',
      salary: '€3,500 - €4,800',
      type: 'Remote',
      logo: '🏢',
      countryFlag: '🇩🇪',
    ),
    Job(
      id: '7',
      title: 'Backend Developer',
      company: 'ServerTech',
      country: 'Austrija',
      city: 'Graz',
      salary: '€3,800 - €5,000',
      type: 'Puno radno vrijeme',
      logo: '🏢',
      countryFlag: '🇦🇹',
    ),
  ];

  List<Job> get _filteredFeaturedJobs {
    // Featured jobs are always shown regardless of search query
    return _featuredJobs;
  }

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _startCarousel();
    });
  }

  @override
  void dispose() {
    _carouselTimer?.cancel();
    _pageController.dispose();
    super.dispose();
  }

  void _startCarousel() {
    if (_shouldShowCarousel()) {
      // Cancel existing timer first
      _carouselTimer?.cancel();
      
      // Start from middle of infinite list for smooth infinite scrolling
      final startIndex = (_filteredFeaturedJobs.length * 500);
      if (_pageController.hasClients) {
        _pageController.jumpToPage(startIndex);
        _currentIndex = startIndex;
      }
      
      _carouselTimer = Timer.periodic(const Duration(seconds: 3), (timer) {
        if (_pageController.hasClients) {
          _currentIndex++;
          _pageController.animateToPage(
            _currentIndex,
            duration: const Duration(milliseconds: 800),
            curve: Curves.easeInOut,
          );
        }
      });
    }
  }


  bool _shouldShowCarousel() {
    final screenWidth = MediaQuery.of(context).size.width;
    final isTablet = screenWidth >= 768;
    
    if (_filteredFeaturedJobs.length == 1) {
      return false; // Always static for 1 job
    }
    
    if (isTablet) {
      return _filteredFeaturedJobs.length >= 3; // Carousel for 3+ jobs on tablet
    } else {
      return _filteredFeaturedJobs.length >= 2; // Carousel for 2+ jobs on mobile
    }
  }

  int _getCarouselItemCount() {
    if (_filteredFeaturedJobs.isEmpty) return 0;
    return _shouldShowCarousel() ? _filteredFeaturedJobs.length * 1000 : _filteredFeaturedJobs.length;
  }

  List<Job> get _filteredRegularJobs {
    if (_searchQuery.isEmpty) return _regularJobs;
    
    return _regularJobs.where((job) {
      final query = _searchQuery.toLowerCase();
      return job.title.toLowerCase().contains(query) ||
             job.company.toLowerCase().contains(query) ||
             job.city.toLowerCase().contains(query);
    }).toList();
  }

  void _showFilterBottomSheet(BuildContext context) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (context) => Container(
        decoration: BoxDecoration(
          color: DesignConstants.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(DesignConstants.radiusHuge),
            topRight: Radius.circular(DesignConstants.radiusHuge),
          ),
          boxShadow: DesignConstants.modernModalShadow,
        ),
        child: Padding(
          padding: EdgeInsets.all(DesignConstants.spaceXXL),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Center(
                child: Container(
                  width: 48,
                  height: 5,
                  decoration: BoxDecoration(
                    color: DesignConstants.modernPrimaryBlue.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(DesignConstants.radiusSmall),
                  ),
                ),
              ),
              SizedBox(height: DesignConstants.spaceXL),
              Text(
                'Filteri za pretragu',
                style: DesignConstants.headlineSmall.copyWith(
                  letterSpacing: -0.5,
                ),
              ),
              SizedBox(height: DesignConstants.spaceXL),
              _buildFilterSection(
                'Lokacija',
                ['Njemačka', 'Austrija', 'Švajcarska'],
              ),
              SizedBox(height: DesignConstants.spaceL),
              _buildFilterSection(
                'Tip posla',
                ['Puno radno vrijeme', 'Part-time', 'Remote', 'Hibridno'],
              ),
              SizedBox(height: DesignConstants.spaceL),
              _buildFilterSection(
                'Plata',
                ['€2000-3000', '€3000-4000', '€4000-5000', '€5000+'],
              ),
              SizedBox(height: DesignConstants.spaceXXL + DesignConstants.spaceS),
              Row(
                children: [
                  Expanded(
                    child: OutlinedButton(
                      onPressed: () {
                        Navigator.pop(context);
                      },
                      style: OutlinedButton.styleFrom(
                        padding: EdgeInsets.symmetric(vertical: DesignConstants.spaceL),
                        side: BorderSide(
                          color: DesignConstants.modernPrimaryBlue.withOpacity(0.3),
                          width: 1.5,
                        ),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(DesignConstants.radiusLarge),
                        ),
                      ),
                      child: Text(
                        'Resetuj',
                        style: DesignConstants.labelLarge.copyWith(
                          color: DesignConstants.modernPrimaryBlue,
                        ),
                      ),
                    ),
                  ),
                  SizedBox(width: DesignConstants.spaceM),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: () {
                        Navigator.pop(context);
                        // Apply filters logic here
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: DesignConstants.modernPrimaryBlue,
                        padding: EdgeInsets.symmetric(vertical: DesignConstants.spaceL),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(DesignConstants.radiusLarge),
                        ),
                        elevation: 0,
                        shadowColor: Colors.transparent,
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(
                            'Primijeni filtre',
                            style: DesignConstants.labelLarge.copyWith(
                              color: DesignConstants.white,
                            ),
                          ),
                          SizedBox(width: DesignConstants.spaceS),
                          Icon(
                            Icons.check_rounded,
                            color: DesignConstants.white,
                            size: DesignConstants.iconMedium,
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
              SizedBox(height: MediaQuery.of(context).viewInsets.bottom),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildFilterSection(String title, List<String> options) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: DesignConstants.titleSmall,
        ),
        SizedBox(height: DesignConstants.spaceS),
        Wrap(
          spacing: DesignConstants.spaceS,
          runSpacing: DesignConstants.spaceS,
          children: options.map((option) {
            return Container(
              margin: EdgeInsets.only(right: DesignConstants.spaceS, bottom: DesignConstants.spaceS),
              child: FilterChip(
                label: Text(
                  option,
                  style: DesignConstants.labelMedium,
                ),
                selected: false,
                onSelected: (selected) {
                  // Handle filter selection
                },
                backgroundColor: DesignConstants.white,
                selectedColor: DesignConstants.modernPrimaryBlue.withOpacity(0.1),
                checkmarkColor: DesignConstants.modernPrimaryBlue,
                labelStyle: DesignConstants.labelMedium,
                side: BorderSide(
                  color: DesignConstants.modernPrimaryBlue.withOpacity(0.2),
                  width: 1.5,
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(DesignConstants.radiusMedium),
                ),
                elevation: 2,
                shadowColor: DesignConstants.modernPrimaryBlue.withOpacity(0.1),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildFeaturedJobsSection() {
    if (_filteredFeaturedJobs.isEmpty) {
      return SizedBox.shrink();
    }

    final screenWidth = MediaQuery.of(context).size.width;
    final isTablet = screenWidth >= 768;
    final showCarousel = _shouldShowCarousel();

    if (!showCarousel) {
      // Static display
      if (isTablet && _filteredFeaturedJobs.length <= 2) {
        // Show both jobs side by side on tablet
        return Container(
          height: 200,
          padding: EdgeInsets.symmetric(horizontal: DesignConstants.spaceL),
          child: Row(
            children: _filteredFeaturedJobs.map((job) {
              final isLast = job == _filteredFeaturedJobs.last;
              return Expanded(
                child: Padding(
                  padding: EdgeInsets.only(right: isLast ? 0 : 16),
                  child: FeaturedJobCard(job: job),
                ),
              );
            }).toList(),
          ),
        );
      } else {
        // Single job - centered
        return Container(
          height: 200,
          padding: EdgeInsets.symmetric(horizontal: DesignConstants.spaceL),
          child: Center(
            child: FeaturedJobCard(job: _filteredFeaturedJobs.first),
          ),
        );
      }
    } else {
      // Carousel display
      return GestureDetector(
        onPanDown: (_) => _carouselTimer?.cancel(),
        onPanEnd: (_) => _startCarousel(),
        child: Container(
          height: 200,
          child: PageView.builder(
            controller: _pageController,
            onPageChanged: (index) {
              setState(() {
                _currentIndex = index % _filteredFeaturedJobs.length;
              });
            },
            itemCount: _getCarouselItemCount(),
            itemBuilder: (context, index) {
              final jobIndex = index % _filteredFeaturedJobs.length;
              return Padding(
                padding: EdgeInsets.symmetric(horizontal: DesignConstants.spaceL),
                child: FeaturedJobCard(job: _filteredFeaturedJobs[jobIndex]),
              );
            },
          ),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: DesignConstants.backgroundLight,
      body: Column(
        children: [
          Expanded(
            child: SingleChildScrollView(
              padding: EdgeInsets.only(bottom: 80),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SizedBox(height: DesignConstants.spaceL),
                  _buildFeaturedJobsSection(),
                  Padding(
                    padding: EdgeInsets.fromLTRB(DesignConstants.spaceL, DesignConstants.spaceL, DesignConstants.spaceL, 0),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Expanded(
                          child: SearchBarWithFilter(
                            onSearch: (query) {
                              setState(() {
                                _searchQuery = query;
                              });
                            },
                          ),
                        ),
                        SizedBox(width: DesignConstants.spaceM),
                        Container(
                          decoration: BoxDecoration(
                            color: DesignConstants.modernPrimaryBlue,
                            borderRadius: BorderRadius.circular(DesignConstants.radiusLarge),
                            boxShadow: DesignConstants.modernBrandButtonShadow,
                          ),
                          child: Material(
                            color: Colors.transparent,
                            child: InkWell(
                              onTap: () {
                                _showFilterBottomSheet(context);
                              },
                              borderRadius: BorderRadius.circular(DesignConstants.radiusLarge),
                              child: Container(
                                padding: EdgeInsets.all(DesignConstants.spaceL),
                                child: Icon(
                                  Icons.tune_rounded,
                                  color: DesignConstants.white,
                                  size: DesignConstants.iconMedium,
                                ),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  SizedBox(height: DesignConstants.spaceL),
                  Padding(
                    padding: EdgeInsets.symmetric(horizontal: DesignConstants.spaceL),
                    child: Column(
                      children: _filteredRegularJobs.map((job) {
                        return RegularJobCard(job: job);
                      }).toList(),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}