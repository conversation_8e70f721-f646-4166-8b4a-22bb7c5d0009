import 'package:flutter/material.dart';
import 'chat_screen.dart';
import '../constants/design_constants.dart';

class MessagesScreen extends StatelessWidget {
  final List<ChatPreview> _conversations = [
    ChatPreview(
      id: '1',
      companyName: 'TechCorp GmbH',
      lastMessage: 'Pozdrav! Interesuje nas vaš profil za poziciju Senior Developer-a. Možemo li da zakazemo razgovor ovaj tjedan?',
      timestamp: '10:30',
      unreadCount: 2,
      companyLogo: '🏢',
      countryFlag: '🇩🇪',
      jobTitle: 'Senior Software Engineer',
      applicationStatus: 'interview',
    ),
    ChatPreview(
      id: '2',
      companyName: 'Alpine Solutions',
      lastMessage: 'Kada bi mogli da zakazemo razgovor? Imamo nekoliko pitanja o vašem iskustvu.',
      timestamp: '09:45',
      unreadCount: 0,
      companyLogo: '⛰️',
      countryFlag: '🇦🇹',
      jobTitle: 'Marketing Manager',
      applicationStatus: 'contract',
    ),
    ChatPreview(
      id: '3',
      companyName: 'DataFlow AG',
      lastMessage: 'Hvala na aplikaciji. Uskoro ćemo vas kontaktirati sa detaljima o sljedećem koraku.',
      timestamp: 'Jučer',
      unreadCount: 1,
      companyLogo: '📊',
      countryFlag: '🇩🇪',
      jobTitle: 'Data Analyst',
      applicationStatus: 'review',
    ),
    ChatPreview(
      id: '4',
      companyName: 'WebSolutions',
      lastMessage: 'Možemo li da organizujemo video call ovaj petak u 14:00?',
      timestamp: 'Jučer',
      unreadCount: 0,
      companyLogo: '💻',
      countryFlag: '🇩🇪',
      jobTitle: 'Frontend Developer',
      applicationStatus: 'onboarding',
    ),
    ChatPreview(
      id: '5',
      companyName: 'StartupHub Vienna',
      lastMessage: 'Odličan portfolio! Zainteresovani smo za saradnju. Kada možemo da se čujemo?',
      timestamp: '2 dana',
      unreadCount: 3,
      companyLogo: '🚀',
      countryFlag: '🇦🇹',
      jobTitle: 'Full Stack Developer',
      applicationStatus: 'interview',
    ),
    ChatPreview(
      id: '6',
      companyName: 'FinTech Solutions',
      lastMessage: 'Zahvaljujemo se na vremenu provedenom na razgovoru. Uskoro ćemo vam se javiti.',
      timestamp: '3 dana',
      unreadCount: 0,
      companyLogo: '💰',
      countryFlag: '🇩🇪',
      jobTitle: 'Backend Developer',
      applicationStatus: 'review',
    ),
    ChatPreview(
      id: '7',
      companyName: 'Alpine Tech Austria',
      lastMessage: 'Pozdrav! Videli smo vaš CV i hteli bismo da razgovaramo o poziciji.',
      timestamp: '4 dana',
      unreadCount: 1,
      companyLogo: '🏔️',
      countryFlag: '🇦🇹',
      jobTitle: 'DevOps Engineer',
      applicationStatus: 'interview',
    ),
    ChatPreview(
      id: '8',
      companyName: 'Berlin Software House',
      lastMessage: 'Da li ste zainteresovani za remote poziciju? Imamo odličnu ponudu.',
      timestamp: '1 sedmica',
      unreadCount: 0,
      companyLogo: '🖥️',
      countryFlag: '🇩🇪',
      jobTitle: 'Mobile Developer',
      applicationStatus: 'contract',
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            DesignConstants.backgroundLight,
            DesignConstants.white,
          ],
        ),
      ),
      child: Column(
        children: [
          Expanded(
            child: _conversations.isEmpty
                ? _buildEmptyState()
                : ListView.builder(
                    padding: EdgeInsets.symmetric(
                      horizontal: DesignConstants.spaceL,
                      vertical: DesignConstants.spaceL,
                    ),
                    itemCount: _conversations.length,
                    itemBuilder: (context, index) {
                      return _buildConversationCard(_conversations[index], context);
                    },
                  ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            width: 80,
            height: 80,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  DesignConstants.modernPrimaryBlue.withOpacity(0.1),
                  DesignConstants.modernPrimaryBlue.withOpacity(0.05),
                ],
              ),
              shape: BoxShape.circle,
              border: Border.all(
                color: DesignConstants.modernPrimaryBlue.withOpacity(0.2),
                width: 2,
              ),
              boxShadow: DesignConstants.shadowSmall,
            ),
            child: Icon(
              Icons.chat_bubble_outline_rounded,
              size: DesignConstants.iconXLarge,
              color: DesignConstants.modernPrimaryBlue,
            ),
          ),
          SizedBox(height: DesignConstants.spaceXXL),
          Text(
            'Nema poruka',
            style: DesignConstants.titleMedium.copyWith(
              color: DesignConstants.black,
            ),
          ),
          SizedBox(height: DesignConstants.spaceS),
          Text(
            'Kada se prijavite za posao, poruke će\nse pojaviti ovdje.',
            textAlign: TextAlign.center,
            style: DesignConstants.bodyMedium.copyWith(
              color: DesignConstants.darkGray,
              height: 1.4,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildConversationCard(ChatPreview chat, BuildContext context) {
    return Container(
      margin: EdgeInsets.only(bottom: DesignConstants.spaceL),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            DesignConstants.white,
            DesignConstants.modernPrimaryBlue.withOpacity(0.005),
          ],
        ),
        borderRadius: BorderRadius.circular(DesignConstants.radiusXLarge),
        border: Border.all(
          color: DesignConstants.modernPrimaryBlue.withOpacity(0.08),
          width: 1.5,
        ),
        boxShadow: [
          BoxShadow(
            color: DesignConstants.modernPrimaryBlue.withOpacity(0.04),
            blurRadius: 12,
            offset: Offset(0, 4),
            spreadRadius: 1,
          ),
          BoxShadow(
            color: Colors.black.withOpacity(0.03),
            blurRadius: 8,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(DesignConstants.radiusXLarge),
          onTap: () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => ChatScreen(
                  companyName: chat.companyName,
                  companyLogo: chat.companyLogo,
                  jobTitle: chat.jobTitle,
                  applicationStatus: chat.applicationStatus,
                ),
              ),
            );
          },
          child: Padding(
            padding: EdgeInsets.all(DesignConstants.spaceL),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Column(
                  children: [
                    Container(
                      width: 56,
                      height: 56,
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                          colors: [
                            DesignConstants.modernPrimaryBlue,
                            DesignConstants.modernPrimaryBlueDark,
                          ],
                        ),
                        borderRadius: BorderRadius.circular(DesignConstants.radiusXLarge),
                        border: Border.all(
                          color: DesignConstants.white,
                          width: 2,
                        ),
                        boxShadow: [
                          BoxShadow(
                            color: DesignConstants.modernPrimaryBlue.withOpacity(0.3),
                            blurRadius: 8,
                            offset: Offset(0, 3),
                          ),
                        ],
                      ),
                      child: Center(
                        child: Text(
                          chat.companyLogo,
                          style: DesignConstants.labelLarge.copyWith(
                            color: DesignConstants.white,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ),
                    SizedBox(height: DesignConstants.spaceXS),
                    Container(
                      width: 32,
                      height: 32,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(16),
                        border: Border.all(
                          color: DesignConstants.lightGray.withOpacity(0.3),
                          width: 1,
                        ),
                      ),
                      child: Center(
                        child: Text(
                          chat.countryFlag ?? '🏢',
                          style: TextStyle(fontSize: 18),
                        ),
                      ),
                    ),
                  ],
                ),
                SizedBox(width: DesignConstants.spaceL),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  chat.companyName,
                                  style: DesignConstants.labelLarge.copyWith(
                                    color: DesignConstants.black,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                                SizedBox(height: 2),
                                Text(
                                  chat.jobTitle,
                                  style: DesignConstants.captionMedium.copyWith(
                                    color: DesignConstants.modernPrimaryBlue,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ],
                            ),
                          ),
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.end,
                            children: [
                              Text(
                                chat.timestamp,
                                style: DesignConstants.captionSmall.copyWith(
                                  color: DesignConstants.darkGray,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                              SizedBox(height: 4),
                              _buildStatusBadge(chat.applicationStatus),
                            ],
                          ),
                        ],
                      ),
                      SizedBox(height: DesignConstants.spaceS),
                      Row(
                        children: [
                          Expanded(
                            child: Text(
                              chat.lastMessage,
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                              style: DesignConstants.bodySmall.copyWith(
                                color: DesignConstants.darkGray,
                                height: 1.4,
                              ),
                            ),
                          ),
                          SizedBox(width: DesignConstants.spaceS),
                          if (chat.unreadCount > 0)
                            Container(
                              padding: EdgeInsets.symmetric(
                                horizontal: DesignConstants.spaceS,
                                vertical: DesignConstants.spaceXS,
                              ),
                              decoration: BoxDecoration(
                                gradient: LinearGradient(
                                  colors: [
                                    DesignConstants.modernPrimaryBlue,
                                    DesignConstants.modernPrimaryBlueDark,
                                  ],
                                ),
                                borderRadius: BorderRadius.circular(DesignConstants.radiusMedium),
                                border: Border.all(
                                  color: DesignConstants.white,
                                  width: 1,
                                ),
                                boxShadow: [
                                  BoxShadow(
                                    color: DesignConstants.modernPrimaryBlue.withOpacity(0.3),
                                    blurRadius: 6,
                                    offset: Offset(0, 2),
                                  ),
                                ],
                              ),
                              child: Text(
                                chat.unreadCount.toString(),
                                style: DesignConstants.captionSmall.copyWith(
                                  color: DesignConstants.white,
                                  fontWeight: FontWeight.w700,
                                ),
                              ),
                            ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildStatusBadge(String status) {
    Map<String, dynamic> statusConfig = _getStatusConfig(status);
    
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: DesignConstants.spaceXS,
        vertical: 2,
      ),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            statusConfig['color'].withOpacity(0.15),
            statusConfig['color'].withOpacity(0.05),
          ],
        ),
        borderRadius: BorderRadius.circular(DesignConstants.radiusSmall),
        border: Border.all(
          color: statusConfig['color'].withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            statusConfig['icon'],
            size: 10,
            color: statusConfig['color'],
          ),
          SizedBox(width: 2),
          Text(
            statusConfig['label'],
            style: DesignConstants.captionSmall.copyWith(
              fontSize: 9,
              fontWeight: FontWeight.w600,
              color: statusConfig['color'],
            ),
          ),
        ],
      ),
    );
  }

  Map<String, dynamic> _getStatusConfig(String status) {
    switch (status) {
      case 'review':
        return {
          'label': 'Pregled',
          'color': DesignConstants.modernWarningYellow,
          'icon': Icons.schedule_rounded,
        };
      case 'interview':
        return {
          'label': 'Intervju',
          'color': DesignConstants.modernPrimaryBlue,
          'icon': Icons.people_rounded,
        };
      case 'contract':
        return {
          'label': 'Ugovor',
          'color': DesignConstants.modernSuccessGreen,
          'icon': Icons.description_rounded,
        };
      case 'onboarding':
        return {
          'label': 'Početak',
          'color': Color(0xFF7C3AED),
          'icon': Icons.flag_rounded,
        };
      default:
        return {
          'label': 'Pregled',
          'color': DesignConstants.modernWarningYellow,
          'icon': Icons.schedule_rounded,
        };
    }
  }
}

class ChatPreview {
  final String id;
  final String companyName;
  final String lastMessage;
  final String timestamp;
  final int unreadCount;
  final String companyLogo;
  final String? countryFlag;
  final String jobTitle;
  final String applicationStatus;

  ChatPreview({
    required this.id,
    required this.companyName,
    required this.lastMessage,
    required this.timestamp,
    required this.unreadCount,
    required this.companyLogo,
    this.countryFlag,
    this.jobTitle = 'Senior Software Engineer',
    this.applicationStatus = 'interview',
  });
}